"use client";

import Image from "next/image";
import {
  CheckCircle,
  Mail,
  Clock,
  FileText,
  Calendar,
  Car,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
}

interface ApplicationSubmittedDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  selectedVehicle: Vehicle | null;
  applicationId?: string;
  onViewStatus?: () => void;
}

export default function ApplicationSubmittedDrawer({
  isOpen,
  onClose,
  selectedVehicle,
  applicationId = "APP" +
    Math.floor(Math.random() * 1000000)
      .toString()
      .padStart(6, "0"),
  onViewStatus,
}: ApplicationSubmittedDrawerProps) {
  const currentDate = new Date().toLocaleDateString("en-ZA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const expectedProcessingTime = "2-3 business days";

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <CheckCircle size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Application Submitted!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              Your vehicle lease application has been successfully submitted
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Vehicle Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md overflow-hidden">
                <div className="h-32 bg-gray-100 relative overflow-hidden">
                  <Image
                    src="/images/cars/suzuki-dzire.webp"
                    alt={`${selectedVehicle?.make} ${selectedVehicle?.model}`}
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-bold text-[#333333]">
                      {selectedVehicle?.make} {selectedVehicle?.model}
                    </h3>
                    <span className="text-sm font-medium bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded-full">
                      Lease Application
                    </span>
                  </div>
                  <p className="text-[#797879] mb-4">
                    {selectedVehicle?.year} Model
                  </p>
                  <div className="flex items-center justify-between p-3 bg-[#e6ffe6] rounded-lg">
                    <div>
                      <p className="text-sm text-[#007A2F] mb-1 font-medium">
                        Weekly Rate
                      </p>
                      <p className="text-xl font-bold text-[#009639]">
                        R{selectedVehicle?.weeklyRate.toLocaleString()}
                      </p>
                    </div>
                    <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm">
                      <Car size={16} className="text-[#009639]" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Application Details */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Application Details
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <span className="text-[#009639] font-bold text-xs">
                          #
                        </span>
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Application ID</p>
                        <p className="text-[#333333] font-medium">
                          {applicationId}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Calendar size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">Submitted Date</p>
                        <p className="text-[#333333] font-medium">
                          {currentDate}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                        <Clock size={12} className="text-[#009639]" />
                      </div>
                      <div>
                        <p className="text-[#797879] text-xs">
                          Expected Processing Time
                        </p>
                        <p className="text-[#333333] font-medium">
                          {expectedProcessingTime}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* What Happens Next */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  What Happens Next?
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        1
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Application Review
                        </p>
                        <p className="text-xs text-[#797879]">
                          Our team will review your application and documents
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        2
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Email Notification
                        </p>
                        <p className="text-xs text-[#797879]">
                          You'll receive an email with the decision and next
                          steps
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        3
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Contract & Payment
                        </p>
                        <p className="text-xs text-[#797879]">
                          If approved, complete payment and sign lease agreement
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className="w-6 h-6 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                        4
                      </div>
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          Vehicle Handover
                        </p>
                        <p className="text-xs text-[#797879]">
                          Schedule and complete vehicle handover process
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Email Confirmation */}
              <div className="rounded-xl bg-green-50 border border-green-200 p-4">
                <div className="flex items-center mb-2">
                  <Mail size={16} className="mr-2 text-green-600" />
                  <h4 className="font-semibold text-green-900">
                    Email Confirmation Sent
                  </h4>
                </div>
                <p className="text-sm text-green-700">
                  A confirmation email has been sent to your registered email
                  address with your application details and reference number.
                </p>
              </div>

              {/* Status Tracking */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <FileText size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Track Your Application
                    </h4>
                  </div>
                  <span
                    className="text-xs px-3 py-1 rounded-full font-medium"
                    style={{
                      borderColor: "#ffd700",
                      borderWidth: "1px",
                      color: "#b8860b",
                    }}
                  >
                    Pending Review
                  </span>
                </div>
                <div
                  className="rounded-xl bg-yellow-50 p-4"
                  style={{ borderColor: "#ffd700", borderWidth: "1px" }}
                >
                  <p className="text-sm" style={{ color: "#b8860b" }}>
                    You can track your application status in your dashboard.
                    We'll update you as soon as there's progress.
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={() => {
                onClose();
                onViewStatus?.();
              }}
              className="w-full bg-[#009639] text-white hover:bg-[#007A2F] py-3 rounded-full font-semibold transition-colors"
            >
              View Application Status
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Return to Home
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
