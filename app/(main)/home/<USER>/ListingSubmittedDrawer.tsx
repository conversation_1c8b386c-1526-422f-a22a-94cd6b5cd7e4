"use client";

import {
  CheckCircle,
  Mail,
  Clock,
  FileText,
  Calendar,
  Car,
  DollarSign,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTitle,
} from "@/components/ui/sheet";

interface VehicleData {
  listingType: "rental" | "fractional" | "";
  rentalPurpose?: "business" | "individual";
  rentalTermType?: "short-term" | "long-term";
  rentalDuration?: string;
  ownershipPercentage?: string;
  allowPartialPurchase?: boolean;
  minimumPurchasePercentage?: string;
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  images: File[];
  documents: any[];
}

interface ListingSubmittedDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  vehicleData: VehicleData | null;
  listingId?: string;
  onViewStatus?: () => void;
}

export default function ListingSubmittedDrawer({
  isOpen,
  onClose,
  vehicleData,
  listingId = "LST" +
    Math.floor(Math.random() * 1000000)
      .toString()
      .padStart(6, "0"),
  onViewStatus,
}: ListingSubmittedDrawerProps) {
  const currentDate = new Date().toLocaleDateString("en-ZA", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const expectedProcessingTime = "3-5 business days";

  // Return early if no vehicle data
  if (!vehicleData) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Success Header */}
          <div className="bg-[#009639] px-6 py-8 flex flex-col items-center">
            <div className="w-20 h-20 bg-[#e6ffe6] rounded-full flex items-center justify-center mb-4 shadow-md">
              <CheckCircle size={40} className="text-[#009639]" />
            </div>
            <SheetTitle className="text-2xl font-bold text-white mb-2">
              Listing Submitted!
            </SheetTitle>
            <SheetDescription className="text-white text-center">
              Your vehicle listing has been successfully submitted for approval
            </SheetDescription>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Listing Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  Listing Summary
                </h4>

                <div className="space-y-3">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <Car size={16} className="text-[#009639]" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        {vehicleData.make} {vehicleData.model}
                      </p>
                      <p className="text-xs text-[#797879]">
                        {vehicleData.year} • {vehicleData.color} •{" "}
                        {Number(vehicleData.mileage).toLocaleString()}km
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <DollarSign size={16} className="text-[#009639]" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        {vehicleData.listingType === "rental"
                          ? "Rental Listing"
                          : "Fractional Ownership"}
                      </p>
                      <p className="text-xs text-[#797879]">
                        {vehicleData.listingType === "rental"
                          ? `${vehicleData.rentalTermType || "Flexible"} rental terms`
                          : `${vehicleData.ownershipPercentage || "Custom"}% ownership share`}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3">
                      <span className="text-[#009639] font-bold text-xs">
                        #
                      </span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Listing ID: {listingId}
                      </p>
                      <p className="text-xs text-[#797879]">
                        Reference number for tracking
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* What Happens Next */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-3">
                  What Happens Next?
                </h4>

                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-white font-bold text-sm">1</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Document Verification
                      </p>
                      <p className="text-xs text-[#797879]">
                        Our team will verify your vehicle documents and photos
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-white font-bold text-sm">2</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Vehicle Inspection
                      </p>
                      <p className="text-xs text-[#797879]">
                        Schedule a physical inspection of your vehicle
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-white font-bold text-sm">3</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        Listing Approval
                      </p>
                      <p className="text-xs text-[#797879]">
                        Your vehicle goes live on the platform
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="w-8 h-8 bg-[#009639] rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                      <span className="text-white font-bold text-sm">4</span>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-[#333333]">
                        {vehicleData.listingType === "rental"
                          ? "Match with Renters"
                          : "Match with Buyers"}
                      </p>
                      <p className="text-xs text-[#797879]">
                        {vehicleData.listingType === "rental"
                          ? "Start receiving rental applications from qualified drivers"
                          : "Start receiving purchase offers from interested buyers"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Important Information */}
              <div className="rounded-xl bg-yellow-50 border border-yellow-200 p-4">
                <div className="flex items-start">
                  <Mail size={16} className="text-yellow-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800 mb-1">
                      Email Confirmation Sent
                    </p>
                    <p className="text-xs text-yellow-700">
                      We've sent a confirmation email with your listing details
                      and next steps.
                    </p>
                  </div>
                </div>
              </div>

              {/* Processing Time */}
              <div className="rounded-xl bg-blue-50 border border-blue-200 p-4">
                <div className="flex items-start">
                  <Clock size={16} className="text-blue-600 mr-2 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800 mb-1">
                      Expected Processing Time
                    </p>
                    <p className="text-xs text-blue-700">
                      {expectedProcessingTime} for document verification and
                      approval
                    </p>
                  </div>
                </div>
              </div>

              {/* Submission Details */}
              <div className="rounded-xl bg-gray-50 border border-gray-200 p-4">
                <div className="flex items-center justify-between text-xs text-[#797879]">
                  <div className="flex items-center">
                    <Calendar size={12} className="mr-1" />
                    <span>Submitted: {currentDate}</span>
                  </div>
                  <div className="flex items-center">
                    <FileText size={12} className="mr-1" />
                    <span>Status: Pending Review</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <button
                  onClick={() => {
                    onClose();
                    onViewStatus?.();
                  }}
                  className="w-full bg-[#009639] text-white hover:bg-[#007A2F] py-3 rounded-full font-semibold transition-colors"
                >
                  View Listing Status
                </button>
                <button
                  onClick={onClose}
                  className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
                >
                  Return to Home
                </button>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
