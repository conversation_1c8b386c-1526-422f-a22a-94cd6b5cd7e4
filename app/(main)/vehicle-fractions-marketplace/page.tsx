"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  ArrowLeft,
  Search,
  Filter,
  Car,
  MapPin,
  ChevronRight,
  Crown,
  Tag,
} from "lucide-react";
import { getAllListingsDrizzle, type ListingRead } from "@/drizzle-actions/listings";
import { getListingInterestCountsDrizzle, getUserInterestStatusDrizzle } from "@/drizzle-actions/listing-interest";
import ListingImage from "@/components/listing-image";
import ListingInterestButton from "@/components/listing-interest-button";
import { useCurrentUser } from "@/hooks/use-current-user";

// Helper function to format listing types for display
const formatListingType = (type: string): string => {
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "Short-term Lease";
    case "LONG_TERM_LEASE_OUT":
      return "Long-term Lease";
    case "CO_OWNERSHIP_SALE":
      return "Co-ownership Sale";
    default:
      return type.replace(/_/g, " ");
  }
};

// Helper function to get listing type color
const getListingTypeColor = (type: string): string => {
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "bg-blue-100 text-blue-800";
    case "LONG_TERM_LEASE_OUT":
      return "bg-purple-100 text-purple-800";
    case "CO_OWNERSHIP_SALE":
      return "bg-green-100 text-green-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export default function VehicleFractionsMarketplaceScreen() {
  const router = useRouter();
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();
  const [searchQuery, setSearchQuery] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [loading, setLoading] = useState(true);
  const [vehicleListings, setVehicleListings] = useState<ListingRead[]>([]);
  const [interestCounts, setInterestCounts] = useState<Record<number, number>>({});
  const [userInterestStatus, setUserInterestStatus] = useState<Record<number, boolean>>({});
  const [filters, setFilters] = useState({
    vehicleType: "all",
    priceRange: "all",
    ownershipPercentage: "all",
    location: "all",
  });

  // Fetch listings from database
  useEffect(() => {
    const fetchListings = async () => {
      try {
        setLoading(true);
        const result = await getAllListingsDrizzle({
          page: 1,
          limit: 20,
          audience: "CONSUMER",
          // Show all listing types in marketplace
          sortBy: "createdAt",
          sortOrder: "desc",
        });
        setVehicleListings(result.records);

        // Fetch interest counts for all listings
        const listingIds = result.records.map(listing => listing.id);
        if (listingIds.length > 0) {
          const counts = await getListingInterestCountsDrizzle(listingIds);
          setInterestCounts(counts);

          // Fetch user interest status if user is logged in
          if (currentUserPartyId) {
            const userStatus = await getUserInterestStatusDrizzle(listingIds, currentUserPartyId);
            setUserInterestStatus(userStatus);
          }
        }
      } catch (error) {
        console.error("Error fetching listings:", error);
      } finally {
        setLoading(false);
      }
    };

    if (!userLoading) {
      fetchListings();
    }
  }, [currentUserPartyId, userLoading]);

  // Filter listings based on search and filters
  const filteredListings = vehicleListings.filter((listing) => {
    const vehicleName = `${listing.vehicle?.model?.make?.name} ${listing.vehicle?.model?.model}`;
    const matchesSearch = vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    
    // Add filter logic here based on the filters state
    // For now, just return search matches
    return matchesSearch;
  });

  const handleFilterChange = (filter: string, value: string) => {
    setFilters({
      ...filters,
      [filter]: value,
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-[#797879]">Loading vehicle fractions...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center border-b border-[#007A2F]">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Vehicle Fractions</h1>
      </div>

      {/* Search and Filter */}
      <div className="p-4 bg-white border-b border-[#f2f2f2]">
        <div className="flex space-x-2 mb-4">
          <div className="flex-1 bg-[#f2f2f2] rounded-full px-4 py-2 flex items-center">
            <Search size={18} className="text-[#009639] mr-2" />
            <input
              type="text"
              placeholder="Search vehicles"
              className="flex-1 bg-transparent focus:outline-none text-[#333333]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <button
            className={`p-2 rounded-full ${
              showFilters
                ? "bg-[#009639] text-white"
                : "bg-[#f2f2f2] text-[#333333]"
            }`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={20} />
          </button>
        </div>

        {showFilters && (
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Vehicle Type
              </label>
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {["all", "sedan", "suv", "bakkie", "hatchback"].map((type) => (
                  <button
                    key={type}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                      filters.vehicleType === type
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() => handleFilterChange("vehicleType", type)}
                  >
                    {type === "all"
                      ? "All Types"
                      : type.charAt(0).toUpperCase() + type.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Price Range
              </label>
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {["all", "under50k", "50k-100k", "100k-200k", "over200k"].map(
                  (range) => (
                    <button
                      key={range}
                      className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                        filters.priceRange === range
                          ? "bg-[#009639] text-white"
                          : "bg-[#f2f2f2] text-[#333333]"
                      }`}
                      onClick={() => handleFilterChange("priceRange", range)}
                    >
                      {range === "all"
                        ? "All Prices"
                        : range === "under50k"
                        ? "Under R50K"
                        : range === "50k-100k"
                        ? "R50K - R100K"
                        : range === "100k-200k"
                        ? "R100K - R200K"
                        : "Over R200K"}
                    </button>
                  )
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Ownership Percentage
              </label>
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {["all", "under25", "25-50", "over50"].map((percentage) => (
                  <button
                    key={percentage}
                    className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                      filters.ownershipPercentage === percentage
                        ? "bg-[#009639] text-white"
                        : "bg-[#f2f2f2] text-[#333333]"
                    }`}
                    onClick={() =>
                      handleFilterChange("ownershipPercentage", percentage)
                    }
                  >
                    {percentage === "all"
                      ? "All Percentages"
                      : percentage === "under25"
                      ? "Under 25%"
                      : percentage === "25-50"
                      ? "25% - 50%"
                      : "Over 50%"}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm text-[#797879] mb-1">
                Location
              </label>
              <div className="flex space-x-2 overflow-x-auto pb-2">
                {["all", "cape-town", "johannesburg", "durban", "pretoria"].map(
                  (location) => (
                    <button
                      key={location}
                      className={`px-3 py-1 rounded-full text-sm whitespace-nowrap ${
                        filters.location === location
                          ? "bg-[#009639] text-white"
                          : "bg-[#f2f2f2] text-[#333333]"
                      }`}
                      onClick={() => handleFilterChange("location", location)}
                    >
                      {location === "all"
                        ? "All Locations"
                        : location === "cape-town"
                        ? "Cape Town"
                        : location === "johannesburg"
                        ? "Johannesburg"
                        : location === "durban"
                        ? "Durban"
                        : "Pretoria"}
                    </button>
                  )
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Listings */}
      <div className="p-4">
        <div className="space-y-4">
          {filteredListings.map((listing) => (
            <div
              key={listing.id}
              className="bg-white rounded-xl shadow-md overflow-hidden border border-gray-100"
            >
              <div className="h-40 bg-[#f2f2f2] relative">
                <ListingImage
                  media={listing.media}
                  alt={`${listing.vehicle?.model?.make?.name} ${listing.vehicle?.model?.model}`}
                  className="object-cover"
                />
                
                {/* User's Own Listing Indicator */}
                {currentUserPartyId && listing.partyId === currentUserPartyId && (
                  <div className="absolute top-3 left-3 bg-[#009639] text-white px-3 py-1 rounded-full text-xs font-medium flex items-center shadow-sm">
                    <Crown size={12} className="mr-1" />
                    Your Listing
                  </div>
                )}
                
                {/* Listing Type Badge */}
                <div className={`absolute top-3 right-3 px-3 py-1 rounded-full text-xs font-medium shadow-sm ${getListingTypeColor(listing.listingType)}`}>
                  <div className="flex items-center">
                    <Tag size={12} className="mr-1" />
                    {formatListingType(listing.listingType)}
                  </div>
                </div>
                
                {/* Ownership Percentage - Only for Co-ownership Sales */}
                {listing.listingType === "CO_OWNERSHIP_SALE" && (
                  <div className="absolute bottom-3 right-3 bg-white px-3 py-1 rounded-full text-xs font-medium text-[#333333] shadow-sm">
                    {Math.round(listing.fractionOffer * 100)}% Ownership
                  </div>
                )}
              </div>

              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="text-[#333333] font-semibold">
                    {listing.vehicle?.manufacturingYear} {listing.vehicle?.model?.make?.name} {listing.vehicle?.model?.model}
                  </h3>
                  <span className="text-lg font-bold text-[#009639]">
                    R{listing.askingPrice.toLocaleString()}
                    {(listing.listingType === "SHORT_TERM_LEASE_OUT" || listing.listingType === "LONG_TERM_LEASE_OUT") && (
                      <span className="text-sm font-medium">/month</span>
                    )}
                  </span>
                </div>

                <div className="flex items-center text-sm text-[#797879] mb-3">
                  <MapPin size={14} className="mr-1" />
                  {listing.vehicle?.countryOfRegistration || "Location not specified"}
                </div>

                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2 shadow-sm">
                      <span className="text-[#009639] font-medium">
                        {listing.owner?.firstName?.charAt(0) || "?"}
                      </span>
                    </div>
                    <div>
                      <p className="text-[#333333] text-sm">
                        {listing.owner?.firstName} {listing.owner?.lastName}
                      </p>
                      <div className="flex items-center">
                        <svg
                          width="12"
                          height="12"
                          viewBox="0 0 24 24"
                          fill="#FFD700"
                        >
                          <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" />
                        </svg>
                        <span className="ml-1 text-xs text-[#797879]">4.5</span>
                      </div>
                    </div>
                  </div>
                  <ListingInterestButton
                    listingId={listing.id}
                    listingAuthorPartyId={listing.partyId}
                    currentUserPartyId={currentUserPartyId}
                    initialInterestCount={interestCounts[listing.id] || 0}
                    initialUserHasInterest={userInterestStatus[listing.id] || false}
                    variant="button"
                    className="text-sm px-3 py-1"
                  />
                </div>

                <button
                  className="w-full border border-[#009639] text-[#009639] py-2 rounded-full text-sm font-medium flex items-center justify-center shadow-sm"
                  onClick={() => {
                    // If it's the user's own listing, go to owner view
                    if (currentUserPartyId && listing.partyId === currentUserPartyId) {
                      router.push(`/my-listing/${listing.id}`);
                    } else {
                      router.push(`/vehicle-fraction-details/${listing.id}`);
                    }
                  }}
                >
                  {currentUserPartyId && listing.partyId === currentUserPartyId 
                    ? "Manage Listing" 
                    : "View Details"
                  }{" "}
                  <ChevronRight size={16} className="ml-1 text-[#009639]" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredListings.length === 0 && (
          <div className="bg-white rounded-xl shadow-sm p-6 flex flex-col items-center justify-center">
            <Car size={40} className="text-[#d6d9dd] mb-3" />
            <p className="text-[#797879] text-center mb-4">
              No vehicle fractions match your search
            </p>
            <button
              className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white px-4 py-2 rounded-full text-sm font-medium shadow-sm"
              onClick={() => {
                setSearchQuery("");
                setFilters({
                  vehicleType: "all",
                  priceRange: "all",
                  ownershipPercentage: "all",
                  location: "all",
                });
              }}
            >
              Clear Filters
            </button>
          </div>
        )}
      </div>

      {/* Add Vehicle Button */}
      <div className="fixed bottom-20 right-4">
        <button
          className="bg-gradient-to-r from-[#009639] to-[#007A2F] text-white p-4 rounded-full shadow-lg"
          onClick={() => router.push("/list-vehicle")}
        >
          <Car size={24} />
        </button>
      </div>
    </div>
  );
}
