"use client";

import { useState, useEffect } from "react";
import { useRouter, use<PERSON>ara<PERSON> } from "next/navigation";
import {
  ArrowLeft,
  Edit,
  Users,
  Calendar,
  DollarSign,
  Eye,
  Phone,
  Mail,
  MapPin,
  Car,
  AlertCircle,
  CheckCircle,
  X,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  getListingByIdDrizzle,
  type ListingRead,
} from "@/drizzle-actions/listings";
import {
  getListingInterestExpressionsDrizzle,
  type ListingInterestExpressionRead,
} from "@/drizzle-actions/listing-interest";
import { endListing } from "@/actions/listing-management";
import { useCurrentUser } from "@/hooks/use-current-user";
import ListingImage from "@/components/listing-image";

// Helper function to mask phone numbers
const maskPhoneNumber = (phone: string): string => {
  if (!phone) return "Not provided";
  // Show first 3 and last 2 digits: +27 123 xxx xx89
  const cleaned = phone.replace(/\D/g, "");
  if (cleaned.length >= 5) {
    const start = cleaned.substring(0, 3);
    const end = cleaned.substring(cleaned.length - 2);
    const middle = "x".repeat(Math.max(0, cleaned.length - 5));
    return `${start}${middle}${end}`;
  }
  return "xxx xxxx";
};

// Helper function to mask email addresses
const maskEmail = (email: string): string => {
  if (!email) return "Not provided";
  const [localPart, domain] = email.split("@");
  if (!domain) return "<EMAIL>";
  
  const maskedLocal = localPart.length > 2 
    ? `${localPart[0]}${"x".repeat(localPart.length - 2)}${localPart[localPart.length - 1]}`
    : "xxx";
  
  return `${maskedLocal}@${domain}`;
};

// Helper function to format listing types
const formatListingType = (type: string): string => {
  switch (type) {
    case "SHORT_TERM_LEASE_OUT":
      return "Short-term Lease";
    case "LONG_TERM_LEASE_OUT":
      return "Long-term Lease";
    case "CO_OWNERSHIP_SALE":
      return "Co-ownership Sale";
    default:
      return type.replace(/_/g, " ");
  }
};

export default function MyListingDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const listingId = parseInt(params.listingId as string);
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();

  const [listing, setListing] = useState<ListingRead | null>(null);
  const [interestedParties, setInterestedParties] = useState<ListingInterestExpressionRead[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    const fetchListingData = async () => {
      if (userLoading || !currentUserPartyId) return;
      
      try {
        setLoading(true);
        setError(null);

        // Fetch listing details
        const listingData = await getListingByIdDrizzle(listingId);
        if (!listingData) {
          setError("Listing not found");
          return;
        }

        // Check if current user owns this listing
        if (listingData.partyId !== currentUserPartyId) {
          setError("You don't have permission to view this listing");
          return;
        }

        setListing(listingData);

        // Fetch interested parties
        const interested = await getListingInterestExpressionsDrizzle(listingId);
        setInterestedParties(interested);

      } catch (error) {
        console.error("Error fetching listing data:", error);
        setError("Failed to load listing details");
      } finally {
        setLoading(false);
      }
    };

    fetchListingData();
  }, [listingId, currentUserPartyId, userLoading]);

  const handleEditListing = () => {
    router.push(`/edit-listing/${listingId}`);
  };

  const handleEndListing = async () => {
    if (!listing || actionLoading) return;
    
    const confirmed = window.confirm("Are you sure you want to end this listing? This action cannot be undone.");
    if (!confirmed) return;

    try {
      setActionLoading(true);
      
      const result = await endListing(listingId);
      
      if (result.success) {
        alert("Listing ended successfully!");
        router.push("/opportunities?tab=vehicles");
      } else {
        alert(`Failed to end listing: ${result.message}`);
      }
      
    } catch (error) {
      console.error("Error ending listing:", error);
      alert("Failed to end listing. Please try again.");
    } finally {
      setActionLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-[#797879]">Loading listing details...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        <div className="bg-[#009639] px-6 py-4 flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Listing Details</h1>
        </div>
        <div className="p-6">
          <Alert className="border-red-500 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!listing) return null;

  const isListingActive = new Date(listing.effectiveTo) > new Date();

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center justify-between">
        <div className="flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">My Listing</h1>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={handleEditListing}
            variant="outline"
            size="sm"
            className="bg-white/10 border-white/20 text-white hover:bg-white/20"
          >
            <Edit size={16} className="mr-2" />
            Edit
          </Button>
          {isListingActive && (
            <Button
              onClick={handleEndListing}
              variant="outline"
              size="sm"
              className="bg-red-500/10 border-red-500/20 text-white hover:bg-red-500/20"
              disabled={actionLoading}
            >
              <X size={16} className="mr-2" />
              {actionLoading ? "Ending..." : "End Listing"}
            </Button>
          )}
        </div>
      </div>

      <div className="p-4 space-y-6">
        {/* Listing Status */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {isListingActive ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="text-green-600 font-medium">Active Listing</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-red-600" />
                    <span className="text-red-600 font-medium">Listing Ended</span>
                  </>
                )}
              </div>
              <Badge variant="outline">
                {formatListingType(listing.listingType)}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Vehicle Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5 text-[#009639]" />
              Vehicle Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Vehicle Image */}
              <div className="h-48 bg-gray-100 rounded-lg overflow-hidden relative">
                <ListingImage
                  media={listing.media}
                  alt={`${listing.vehicle?.model?.make?.name} ${listing.vehicle?.model?.model}`}
                  className="object-cover w-full h-full"
                />
                {listing.media && listing.media.length > 1 && (
                  <div className="absolute bottom-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-xs">
                    +{listing.media.length - 1} more photos
                  </div>
                )}
              </div>

              {/* Vehicle Info */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-semibold text-lg">
                      {listing.vehicle?.manufacturingYear} {listing.vehicle?.model?.make?.name} {listing.vehicle?.model?.model}
                    </h3>
                    <p className="text-gray-600">
                      {listing.vehicle?.color} • {listing.condition}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-[#009639]">
                      R{listing.askingPrice.toLocaleString()}
                      {(listing.listingType === "SHORT_TERM_LEASE_OUT" || listing.listingType === "LONG_TERM_LEASE_OUT") && (
                        <span className="text-lg">/month</span>
                      )}
                    </div>
                    {listing.listingType === "CO_OWNERSHIP_SALE" && (
                      <div className="text-sm text-gray-600">
                        {Math.round(listing.fractionOffer * 100)}% Ownership
                      </div>
                    )}
                  </div>
                </div>

                {/* Vehicle Details Grid */}
                <div className="grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg">
                  <div>
                    <span className="text-gray-500">VIN:</span>
                    <div className="font-medium">{listing.vehicle?.vinNumber}</div>
                  </div>
                  {listing.vehicle?.vehicleRegistration && (
                    <div>
                      <span className="text-gray-500">Registration:</span>
                      <div className="font-medium">{listing.vehicle.vehicleRegistration}</div>
                    </div>
                  )}
                  {listing.mileage && (
                    <div>
                      <span className="text-gray-500">Mileage:</span>
                      <div className="font-medium">{listing.mileage.toLocaleString()} km</div>
                    </div>
                  )}
                  {listing.vehicle?.model?.transmission && (
                    <div>
                      <span className="text-gray-500">Transmission:</span>
                      <div className="font-medium">{listing.vehicle.model.transmission}</div>
                    </div>
                  )}
                </div>

                <div className="flex items-center text-gray-600">
                  <MapPin size={16} className="mr-2" />
                  {listing.vehicle?.countryOfRegistration || "Location not specified"}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Listing Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-[#009639]" />
              Listing Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-[#009639]">
                  {interestedParties.length}
                </div>
                <div className="text-sm text-gray-600">Interested Parties</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-[#009639]">0</div>
                <div className="text-sm text-gray-600">Views</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-[#009639]">
                  {Math.ceil((new Date(listing.effectiveTo).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}
                </div>
                <div className="text-sm text-gray-600">Days Remaining</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interested Parties */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-[#009639]" />
              Interested Parties ({interestedParties.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            {interestedParties.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="mx-auto h-12 w-12 mb-4 opacity-30" />
                <p>No one has expressed interest yet.</p>
                <p className="text-sm">Share your listing to get more visibility!</p>
              </div>
            ) : (
              <div className="space-y-3">
                {interestedParties.map((party) => (
                  <div
                    key={party.id}
                    className="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-[#009639] rounded-full flex items-center justify-center text-white font-medium">
                        {party.person?.firstName?.charAt(0) || "?"}
                      </div>
                      <div>
                        <h4 className="font-medium">
                          {party.person?.firstName} {party.person?.lastName}
                        </h4>
                        <div className="text-sm text-gray-600 space-y-1">
                          {party.person?.phone && (
                            <div className="flex items-center">
                              <Phone size={12} className="mr-1" />
                              {maskPhoneNumber(party.person.phone)}
                            </div>
                          )}
                          {party.person?.email && (
                            <div className="flex items-center">
                              <Mail size={12} className="mr-1" />
                              {maskEmail(party.person.email)}
                            </div>
                          )}
                          {!party.person?.phone && !party.person?.email && (
                            <div className="text-xs text-gray-400">
                              Contact details not provided
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      <div>Interested</div>
                      <div>
                        {party.createdAt ? new Date(party.createdAt).toLocaleDateString() : "Recently"}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Listing Timeline */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-[#009639]" />
              Listing Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Listed on:</span>
                <span className="font-medium">
                  {new Date(listing.effectiveFrom).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Expires on:</span>
                <span className="font-medium">
                  {new Date(listing.effectiveTo).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Status:</span>
                <Badge variant={isListingActive ? "default" : "destructive"}>
                  {isListingActive ? "Active" : "Expired"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 