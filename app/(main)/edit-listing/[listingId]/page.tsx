"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { <PERSON><PERSON>ef<PERSON>, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  getListingByIdDrizzle,
  type ListingRead,
} from "@/drizzle-actions/listings";
import { updateListing } from "@/actions/listing-management";
import { ConditionEnum, AudienceEnum } from "@/types/listings";
import { useCurrentUser } from "@/hooks/use-current-user";

interface EditListingFormData {
  askingPrice: string;
  fractionOffer: string;
  effectiveFrom: string;
  effectiveTo: string;
  condition: string;
  mileage: string;
  audience: string;
  description: string;
}

export default function EditListingPage() {
  const router = useRouter();
  const params = useParams();
  const listingId = parseInt(params.listingId as string);
  const { partyId: currentUserPartyId, isLoading: userLoading } = useCurrentUser();

  const [listing, setListing] = useState<ListingRead | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<EditListingFormData>({
    askingPrice: "",
    fractionOffer: "",
    effectiveFrom: "",
    effectiveTo: "",
    condition: "",
    mileage: "",
    audience: "",
    description: "",
  });

  useEffect(() => {
    const fetchListing = async () => {
      if (userLoading || !currentUserPartyId) return;
      
      try {
        setLoading(true);
        setError(null);

        const listingData = await getListingByIdDrizzle(listingId);
        if (!listingData) {
          setError("Listing not found");
          return;
        }

        // Check if current user owns this listing
        if (listingData.partyId !== currentUserPartyId) {
          setError("You don't have permission to edit this listing");
          return;
        }

        setListing(listingData);
        
        // Populate form with existing data
        setFormData({
          askingPrice: listingData.askingPrice.toString(),
          fractionOffer: (listingData.fractionOffer * 100).toString(),
          effectiveFrom: listingData.effectiveFrom,
          effectiveTo: listingData.effectiveTo,
          condition: listingData.condition,
          mileage: listingData.mileage?.toString() || "",
          audience: listingData.audience,
          description: "", // We don't have description in the current schema
        });

      } catch (error) {
        console.error("Error fetching listing:", error);
        setError("Failed to load listing details");
      } finally {
        setLoading(false);
      }
    };

    fetchListing();
  }, [listingId, currentUserPartyId, userLoading]);

  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!listing || saving) return;

    setSaving(true);
    setError(null);

    try {
      const result = await updateListing({
        listingId,
        askingPrice: parseFloat(formData.askingPrice),
        fractionOffer: listing.listingType === "CO_OWNERSHIP_SALE" ? parseFloat(formData.fractionOffer) : undefined,
        effectiveFrom: formData.effectiveFrom,
        effectiveTo: formData.effectiveTo,
        condition: formData.condition as ConditionEnum,
        mileage: formData.mileage ? parseFloat(formData.mileage) : undefined,
        audience: formData.audience as AudienceEnum,
      });

      if (result.success) {
        setSuccess(true);
        setTimeout(() => {
          router.push(`/my-listing/${listingId}`);
        }, 2000);
      } else {
        setError(result.message);
      }

    } catch (error) {
      console.error("Error updating listing:", error);
      setError("Failed to update listing. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <div className="text-[#797879]">Loading listing...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-[#f5f5f5]">
        <div className="bg-[#009639] px-6 py-4 flex items-center">
          <button className="mr-4" onClick={() => router.back()}>
            <ArrowLeft size={24} className="text-white" />
          </button>
          <h1 className="text-xl font-bold text-white">Edit Listing</h1>
        </div>
        <div className="p-6">
          <Alert className="border-red-500 bg-red-50">
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-[#f5f5f5] flex items-center justify-center">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <div className="text-green-600 text-6xl mb-4">✓</div>
            <h2 className="text-xl font-bold text-gray-800 mb-2">Listing Updated!</h2>
            <p className="text-gray-600 mb-4">
              Your listing has been successfully updated.
            </p>
            <p className="text-sm text-gray-500">Redirecting...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!listing) return null;

  return (
    <div className="min-h-screen bg-[#f5f5f5]">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex items-center">
        <button className="mr-4" onClick={() => router.back()}>
          <ArrowLeft size={24} className="text-white" />
        </button>
        <h1 className="text-xl font-bold text-white">Edit Listing</h1>
      </div>

      <div className="p-4">
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Vehicle Info (Read-only) */}
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-semibold">
                  {listing.vehicle?.manufacturingYear} {listing.vehicle?.model?.make?.name} {listing.vehicle?.model?.model}
                </h3>
                <p className="text-sm text-gray-600">
                  {listing.vehicle?.color} • VIN: {listing.vehicle?.vinNumber}
                </p>
                <p className="text-xs text-gray-500 mt-2">
                  Vehicle details cannot be changed. Contact support if you need to modify vehicle information.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="askingPrice">
                  {listing.listingType === "CO_OWNERSHIP_SALE" ? "Total Price" : "Monthly Price"} (R)
                </Label>
                <Input
                  id="askingPrice"
                  type="number"
                  value={formData.askingPrice}
                  onChange={(e) => handleInputChange("askingPrice", e.target.value)}
                  placeholder="Enter price"
                  required
                />
              </div>

              {listing.listingType === "CO_OWNERSHIP_SALE" && (
                <div>
                  <Label htmlFor="fractionOffer">Ownership Percentage (%)</Label>
                  <Input
                    id="fractionOffer"
                    type="number"
                    min="1"
                    max="100"
                    value={formData.fractionOffer}
                    onChange={(e) => handleInputChange("fractionOffer", e.target.value)}
                    placeholder="Enter percentage"
                    required
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Vehicle Details */}
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="condition">Condition</Label>
                <Select value={formData.condition} onValueChange={(value) => handleInputChange("condition", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="used">Used</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="mileage">Mileage (km)</Label>
                <Input
                  id="mileage"
                  type="number"
                  value={formData.mileage}
                  onChange={(e) => handleInputChange("mileage", e.target.value)}
                  placeholder="Enter mileage"
                />
              </div>
            </CardContent>
          </Card>

          {/* Listing Settings */}
          <Card>
            <CardHeader>
              <CardTitle>Listing Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="audience">Target Audience</Label>
                <Select value={formData.audience} onValueChange={(value) => handleInputChange("audience", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select audience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CONSUMER">Consumer</SelectItem>
                    <SelectItem value="BUSINESS">Business</SelectItem>
                    <SelectItem value="E_HAILING">E-Hailing</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="effectiveFrom">Available From</Label>
                  <Input
                    id="effectiveFrom"
                    type="date"
                    value={formData.effectiveFrom}
                    onChange={(e) => handleInputChange("effectiveFrom", e.target.value)}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="effectiveTo">Available Until</Label>
                  <Input
                    id="effectiveTo"
                    type="date"
                    value={formData.effectiveTo}
                    onChange={(e) => handleInputChange("effectiveTo", e.target.value)}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Add any additional details about your listing..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={saving}
              className="flex-1 bg-[#009639] hover:bg-[#007A2F]"
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
} 