"use server";
import MemberManagementScreen from "./member-management";
import { getContactPointsByPartyIds } from "@/actions/contact-points";
import { getCompanyOwnershipsByCompanies } from "@/actions/company-ownership";

export default async function MemberManagement({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const ownerships = await getCompanyOwnershipsByCompanies([+id]);
  const contacts = await getContactPointsByPartyIds([34, 2]);
  return (
    <MemberManagementScreen
      ownerships={ownerships}
      contacts={contacts}
      groupId={+id}
    />
  );
}
