"use client";

import React, { useState } from "react";
import {
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  User,
  Car,
  FileText,
  MoreVertical,
  ChevronDown,
  Users,
  TrendingUp,
  AlertCircle,
  Mail,
} from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface Application {
  id: string;
  applicantName: string;
  applicantEmail: string;
  vehicleName: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  weeklyRate?: number;
  applicationType:
    | "vehicle_lease"
    | "rental_application"
    | "co_ownership_application"
    | "vehicle_listing";
  listingType?: "rental" | "fractional" | "lease-to-own";
  experience?: {
    hasExperience: boolean;
    company?: string;
    duration?: string;
    profileNumber?: string;
  };
  documents?: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  // Additional fields for EARN side applications
  applicantAge?: number;
  applicantGender?: "male" | "female" | "other";
  drivingExperienceYears?: number;
  documentsVerified?: boolean;
  matchesPreferences?: {
    age: boolean;
    gender: boolean;
    experience: boolean;
  };
}

export default function ApplicationsPage() {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [filterType, setFilterType] = useState<
    | "all"
    | "vehicle_lease"
    | "rental_application"
    | "co_ownership_application"
    | "vehicle_listing"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual data fetching
  const [applications] = useState<Application[]>([
    // NEED side - Vehicle lease applications (existing e-hailing functionality)
    {
      id: "1",
      applicantName: "John Doe",
      applicantEmail: "<EMAIL>",
      vehicleName: "Suzuki Dzire 2023",
      applicationDate: "2024-01-15",
      status: "pending",
      applicationType: "vehicle_lease",
      weeklyRate: 2700,
      experience: {
        hasExperience: true,
        company: "Uber",
        duration: "2-5-years",
        profileNumber: "78432",
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "PrDP", uploaded: true, verified: false },
      ],
    },
    // NEED side - Rental application (person wanting to rent a vehicle)
    {
      id: "2",
      applicantName: "Sarah Mitchell",
      applicantEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2022",
      applicationDate: "2024-01-16",
      status: "pending",
      applicationType: "rental_application",
      listingType: "rental",
      applicantAge: 32,
      applicantGender: "female",
      drivingExperienceYears: 8,
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: true,
        experience: true,
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "Proof of Address", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: false },
      ],
    },
    // NEED side - Co-ownership application (person wanting to buy into co-ownership)
    {
      id: "3",
      applicantName: "Michael Davis",
      applicantEmail: "<EMAIL>",
      vehicleName: "Honda Civic 2023",
      applicationDate: "2024-01-14",
      status: "under_review",
      applicationType: "co_ownership_application",
      listingType: "fractional",
      applicantAge: 29,
      applicantGender: "male",
      drivingExperienceYears: 6,
      documentsVerified: true,
      matchesPreferences: {
        age: true,
        gender: false,
        experience: true,
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "Proof of Income", uploaded: true, verified: true },
        { name: "Bank Statement", uploaded: true, verified: true },
      ],
    },
    // EARN side - Vehicle listing application (person wanting to list their vehicle)
    {
      id: "4",
      applicantName: "Lisa Johnson",
      applicantEmail: "<EMAIL>",
      vehicleName: "BMW X3 2021",
      applicationDate: "2024-01-13",
      status: "pending",
      applicationType: "vehicle_listing",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: false },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: false },
      ],
    },
    // Historical data
    {
      id: "5",
      applicantName: "David Wilson",
      applicantEmail: "<EMAIL>",
      vehicleName: "Nissan Almera 2022",
      applicationDate: "2024-01-10",
      status: "approved",
      applicationType: "vehicle_lease",
      decisionDate: "2024-01-12",
      weeklyRate: 2500,
      experience: {
        hasExperience: true,
        company: "Bolt",
        duration: "1-2-years",
        profileNumber: "4.6/5",
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "PrDP", uploaded: true, verified: true },
      ],
    },
    {
      id: "6",
      applicantName: "Jane Smith",
      applicantEmail: "<EMAIL>",
      vehicleName: "Suzuki Dzire 2023",
      applicationDate: "2024-01-14",
      status: "under_review",
      applicationType: "vehicle_lease",
      weeklyRate: 2700,
      experience: {
        hasExperience: false,
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
    },
    {
      id: "3",
      applicantName: "Mike Johnson",
      applicantEmail: "<EMAIL>",
      vehicleName: "Suzuki Dzire 2023",
      applicationDate: "2024-01-10",
      status: "approved",
      applicationType: "vehicle_lease",
      decisionDate: "2024-01-12",
      weeklyRate: 2700,
      experience: {
        hasExperience: true,
        company: "Bolt",
        duration: "1-2-years",
        profileNumber: "56789",
      },
    },
    {
      id: "4",
      applicantName: "Lisa Brown",
      applicantEmail: "<EMAIL>",
      vehicleName: "Suzuki Dzire 2023",
      applicationDate: "2024-01-08",
      status: "rejected",
      applicationType: "vehicle_lease",
      decisionDate: "2024-01-09",
      decisionReason: "Insufficient e-hailing experience",
      weeklyRate: 2700,
      experience: {
        hasExperience: false,
      },
    },
  ]);

  const getStatusColor = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "under_review":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "approved":
        return "bg-green-100 text-green-800 border-green-200";
      case "rejected":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return <Clock size={14} />;
      case "under_review":
        return <Eye size={14} />;
      case "approved":
        return <CheckCircle size={14} />;
      case "rejected":
        return <XCircle size={14} />;
      default:
        return <Clock size={14} />;
    }
  };

  // Filter applications based on current tab
  const activeApplications = applications.filter(
    (app) => app.status === "pending" || app.status === "under_review"
  );

  const historicalApplications = applications.filter(
    (app) => app.status === "approved" || app.status === "rejected"
  );

  const currentApplications =
    currentTab === "active" ? activeApplications : historicalApplications;

  const filteredApplications = currentApplications.filter((app) => {
    const matchesStatusFilter =
      filterStatus === "all" || app.status === filterStatus;
    const matchesTypeFilter =
      filterType === "all" || app.applicationType === filterType;
    const matchesSearch =
      app.applicantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatusFilter && matchesTypeFilter && matchesSearch;
  });

  const getFilterCount = (filter: typeof filterStatus) => {
    if (filter === "all") return currentApplications.length;
    return currentApplications.filter((app) => app.status === filter).length;
  };

  const getTabCount = (tab: "active" | "history") => {
    return tab === "active"
      ? activeApplications.length
      : historicalApplications.length;
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          Application Management
        </h1>
        <div className="flex space-x-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Data
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter size={16} />
            Advanced Filters
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Applications</p>
                <p className="text-2xl font-bold mt-1">{applications.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <FileText className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    applications.filter(
                      (app) =>
                        app.status === "pending" ||
                        app.status === "under_review"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    applications.filter((app) => app.status === "approved")
                      .length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approval Rate</p>
                <p className="text-2xl font-bold mt-1">
                  {Math.round(
                    (applications.filter((app) => app.status === "approved")
                      .length /
                      applications.filter(
                        (app) =>
                          app.status === "approved" || app.status === "rejected"
                      ).length) *
                      100
                  )}
                  %
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs
              value={currentTab}
              onValueChange={(value) =>
                setCurrentTab(value as "active" | "history")
              }
            >
              <TabsList>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <Clock size={16} />
                  Active Applications ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="flex items-center gap-2"
                >
                  <Calendar size={16} />
                  Application History ({getTabCount("history")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search applications..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent w-64"
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Type:{" "}
                    {filterType === "all"
                      ? "All"
                      : filterType === "vehicle_lease"
                        ? "Vehicle Lease"
                        : filterType === "rental_application"
                          ? "Rental"
                          : filterType === "co_ownership_application"
                            ? "Co-ownership"
                            : "Vehicle Listing"}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Types" },
                    { key: "vehicle_lease", label: "Vehicle Lease" },
                    { key: "rental_application", label: "Rental Application" },
                    { key: "co_ownership_application", label: "Co-ownership" },
                    { key: "vehicle_listing", label: "Vehicle Listing" },
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterType(filter.key as typeof filterType)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {
                          applications.filter(
                            (app) =>
                              filter.key === "all" ||
                              app.applicationType === filter.key
                          ).length
                        }
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Status:{" "}
                    {filterStatus === "all"
                      ? "All"
                      : filterStatus.replace("_", " ")}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Applications" },
                    ...(currentTab === "active"
                      ? [
                          { key: "pending", label: "Pending" },
                          { key: "under_review", label: "Under Review" },
                        ]
                      : [
                          { key: "approved", label: "Approved" },
                          { key: "rejected", label: "Rejected" },
                        ]),
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterStatus(filter.key as typeof filterStatus)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {getFilterCount(filter.key as typeof filterStatus)}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <Users size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No Applications Found
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? "Try adjusting your search criteria"
                  : "No applications match the selected filter"}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant</TableHead>
                    <TableHead>Vehicle/Type</TableHead>
                    <TableHead>Applied Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Experience</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Rate/Details</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((application) => (
                    <TableRow key={application.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">
                            {application.applicantName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {application.applicantEmail}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Car size={16} className="text-gray-400 mr-2" />
                          <div>
                            <span className="text-sm font-medium">
                              {application.vehicleName}
                            </span>
                            <div className="text-xs text-gray-500">
                              {application.applicationType ===
                                "vehicle_lease" && "E-hailing Lease"}
                              {application.applicationType ===
                                "rental_application" && "Rental Application"}
                              {application.applicationType ===
                                "co_ownership_application" && "Co-ownership"}
                              {application.applicationType ===
                                "vehicle_listing" && "Vehicle Listing"}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(
                            application.applicationDate
                          ).toLocaleDateString("en-GB", {
                            day: "2-digit",
                            month: "2-digit",
                            year: "numeric",
                          })}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`flex items-center gap-1 w-fit ${getStatusColor(application.status)}`}
                        >
                          {getStatusIcon(application.status)}
                          <span className="capitalize">
                            {application.status.replace("_", " ")}
                          </span>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {application.applicationType === "vehicle_listing" ? (
                          <div className="text-sm text-gray-500">
                            Vehicle Owner
                          </div>
                        ) : application.drivingExperienceYears ? (
                          <div className="flex items-center text-green-600">
                            <CheckCircle size={14} className="mr-1" />
                            <span className="text-sm">
                              {application.drivingExperienceYears} years
                            </span>
                          </div>
                        ) : application.experience?.hasExperience ? (
                          <div className="flex items-center text-green-600">
                            <CheckCircle size={14} className="mr-1" />
                            <span className="text-sm">
                              {application.experience.company} (
                              {application.experience.duration})
                            </span>
                          </div>
                        ) : (
                          <div className="flex items-center text-yellow-600">
                            <AlertCircle size={14} className="mr-1" />
                            <span className="text-sm">No experience</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {application.documents && (
                          <div className="text-sm">
                            <span className="text-green-600">
                              {
                                application.documents.filter((d) => d.uploaded)
                                  .length
                              }
                            </span>
                            <span className="text-gray-400">
                              /{application.documents.length}
                            </span>
                            <span className="text-gray-500 ml-1">uploaded</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        {application.weeklyRate ? (
                          <div>
                            <div className="font-medium">
                              R{application.weeklyRate.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-500">
                              per week
                            </div>
                          </div>
                        ) : application.applicationType ===
                          "vehicle_listing" ? (
                          <div className="text-sm text-gray-500">
                            Listing Review
                          </div>
                        ) : application.applicantAge ? (
                          <div>
                            <div className="text-sm font-medium">
                              Age: {application.applicantAge}
                            </div>
                            <div className="text-xs text-gray-500 capitalize">
                              {application.applicantGender}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500">N/A</div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            asChild
                            className="h-8 w-8 p-0"
                          >
                            <Link
                              href={`/admin/applications/${application.id}`}
                            >
                              <Eye size={16} className="text-gray-600" />
                            </Link>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
