"use client";

import React, { useState, useEffect, useRef } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  Car,
  DollarSign,
  Settings,
  Plus,
  X,
  CheckCircle,
  Upload,
  Camera,
  Loader,
} from "lucide-react";
import { DocumentUpload } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface VehicleCatalogItem {
  id?: string;
  make: string;
  model: string;
  year: number;
  category: "sedan" | "suv" | "hatchback" | "bakkie";
  weeklyRate: number;
  initiationFee: number;
  features: string[];
  suitableFor: string[];
  isActive: boolean;
  description?: string;
  imageUrl?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface VehicleCatalogFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (vehicleData: VehicleCatalogItem) => void;
  editingVehicle?: any | null; // Accept any vehicle data structure
  mode?: "add" | "edit" | "view";
}

export default function VehicleCatalogFormDialog({
  isOpen,
  onClose,
  onConfirm,
  editingVehicle,
  mode = "add",
}: VehicleCatalogFormDialogProps) {
  console.log("🚀 VehicleCatalogFormDialog render:", {
    isOpen,
    mode,
    editingVehicle: editingVehicle?.id,
  });

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const [formData, setFormData] = useState<VehicleCatalogItem>({
    make: "",
    model: "",
    year: new Date().getFullYear(),
    category: "sedan",
    weeklyRate: 0,
    initiationFee: 0,
    features: [],
    suitableFor: [],
    isActive: true,
    description: "",
    imageUrl: "",
  });

  const [newFeature, setNewFeature] = useState("");
  const [newPlatform, setNewPlatform] = useState("");
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [imagePreview, setImagePreview] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    console.log("🔄 Dialog useEffect triggered:", {
      isOpen,
      editingVehicle: editingVehicle?.id,
    });

    if (editingVehicle) {
      setFormData(editingVehicle);
      setImagePreview(editingVehicle.imageUrl || "");
    } else {
      setFormData({
        make: "",
        model: "",
        year: new Date().getFullYear(),
        category: "sedan",
        weeklyRate: 0,
        initiationFee: 0,
        features: [],
        suitableFor: [],
        isActive: true,
        description: "",
        imageUrl: "",
      });
      setImagePreview("");
    }

    // Reset local state when dialog opens/closes
    if (!isOpen) {
      console.log("🧹 Cleaning up dialog state (isOpen = false)");
      setNewFeature("");
      setNewPlatform("");
      setImagePreview("");
    }
  }, [editingVehicle, isOpen]);

  const handleInputChange = (field: keyof VehicleCatalogItem, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData((prev) => ({
        ...prev,
        features: [...prev.features, newFeature.trim()],
      }));
      setNewFeature("");
    }
  };

  const removeFeature = (feature: string) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.filter((f) => f !== feature),
    }));
  };

  const addPlatform = () => {
    if (
      newPlatform.trim() &&
      !formData.suitableFor.includes(newPlatform.trim())
    ) {
      setFormData((prev) => ({
        ...prev,
        suitableFor: [...prev.suitableFor, newPlatform.trim()],
      }));
      setNewPlatform("");
    }
  };

  const removePlatform = (platform: string) => {
    setFormData((prev) => ({
      ...prev,
      suitableFor: prev.suitableFor.filter((p) => p !== platform),
    }));
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      alert("Please select an image file");
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert("Image size must be less than 5MB");
      return;
    }

    try {
      setIsUploadingImage(true);

      // Create preview
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);

      // Upload to S3
      const uploadResult = await DocumentUpload(file, "vehicle-catalog");

      if (uploadResult && uploadResult.path) {
        setFormData((prev) => ({
          ...prev,
          imageUrl: uploadResult.path,
        }));
      } else {
        throw new Error("Upload failed");
      }
    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Failed to upload image. Please try again.");
      // Reset preview on error
      setImagePreview("");
    } finally {
      setIsUploadingImage(false);
    }
  };

  const removeImage = () => {
    setFormData((prev) => ({
      ...prev,
      imageUrl: "",
    }));
    setImagePreview("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleConfirm = () => {
    if (canConfirm()) {
      onConfirm(formData);
      // Don't call onClose here - let the parent handle it after successful submission
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Reset all local state when dialog closes
      setNewFeature("");
      setNewPlatform("");
      setImagePreview("");
      onClose();
    }
  };

  const canConfirm = () => {
    return (
      mode === "view" ||
      (formData.make.trim() &&
        formData.model.trim() &&
        formData.year > 1900 &&
        formData.weeklyRate > 0 &&
        formData.initiationFee >= 0)
    );
  };

  const getDialogTitle = () => {
    switch (mode) {
      case "add":
        return "Add Vehicle Model";
      case "edit":
        return "Edit Vehicle Model";
      case "view":
        return "Vehicle Model Details";
      default:
        return editingVehicle ? "Edit Vehicle Model" : "Add Vehicle Model";
    }
  };

  const getDialogDescription = () => {
    switch (mode) {
      case "add":
        return "Add a new vehicle model to the catalog with specifications and pricing";
      case "edit":
        return "Update the vehicle model specifications and pricing";
      case "view":
        return "View detailed vehicle model information";
      default:
        return editingVehicle
          ? "Update the vehicle model specifications and pricing"
          : "Add a new vehicle model to the catalog with specifications and pricing";
    }
  };

  const isReadOnly = mode === "view";

  const commonFeatures = [
    "Air Conditioning",
    "Power Steering",
    "Electric Windows",
    "Radio/USB",
    "ABS",
    "Airbags",
    "Central Locking",
    "Bluetooth",
    "GPS Navigation",
    "Reverse Camera",
  ];

  const commonPlatforms = ["Uber", "Bolt", "InDriver"];

  console.log("📦 About to render Dialog component with:", {
    isOpen,
    mode,
    canConfirm: canConfirm(),
    dialogTitle: getDialogTitle(),
  });

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <Car className="h-5 w-5 text-[#009639]" />
            {getDialogTitle()}
          </DialogTitle>
          <DialogDescription>{getDialogDescription()}</DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Car className="h-5 w-5 text-[#009639]" />
                Vehicle Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="make">Make *</Label>
                  <Input
                    id="make"
                    value={formData.make}
                    onChange={(e) => handleInputChange("make", e.target.value)}
                    placeholder="e.g., Toyota"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="model">Model *</Label>
                  <Input
                    id="model"
                    value={formData.model}
                    onChange={(e) => handleInputChange("model", e.target.value)}
                    placeholder="e.g., Corolla"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="year">Year *</Label>
                  <Input
                    id="year"
                    type="number"
                    value={formData.year}
                    onChange={(e) =>
                      handleInputChange("year", Number(e.target.value))
                    }
                    min="1900"
                    max={new Date().getFullYear() + 2}
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select
                    onValueChange={(value) =>
                      handleInputChange("category", value)
                    }
                    value={formData.category}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sedan">Sedan</SelectItem>
                      <SelectItem value="suv">SUV</SelectItem>
                      <SelectItem value="hatchback">Hatchback</SelectItem>
                      <SelectItem value="bakkie">Bakkie</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center space-x-2 mt-6">
                  <Input
                    type="checkbox"
                    id="isActive"
                    checked={formData.isActive}
                    onChange={(e) =>
                      handleInputChange("isActive", e.target.checked)
                    }
                    className="rounded border-gray-300"
                    disabled={isReadOnly}
                  />
                  <Label htmlFor="isActive" className="text-sm">
                    Active in catalog
                  </Label>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange("description", e.target.value)
                  }
                  placeholder="Brief description of the vehicle model..."
                  className="mt-1"
                  rows={3}
                  readOnly={isReadOnly}
                />
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Camera className="h-5 w-5 text-[#009639]" />
                Vehicle Image
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                {/* Image Preview */}
                {imagePreview ? (
                  <div className="relative">
                    <div className="w-full h-48 border-2 border-gray-200 rounded-lg overflow-hidden">
                      <img
                        src={imagePreview}
                        alt="Vehicle preview"
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {!isReadOnly && (
                      <button
                        onClick={removeImage}
                        className="absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        title="Remove image"
                      >
                        <X size={16} />
                      </button>
                    )}
                  </div>
                ) : (
                  <div className="w-full h-48 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 hover:border-[#009639] hover:text-[#009639] transition-colors">
                    {isUploadingImage ? (
                      <div className="flex flex-col items-center">
                        <Loader size={32} className="animate-spin mb-2" />
                        <p className="text-sm">Uploading image...</p>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <Upload size={32} className="mb-2" />
                        <p className="text-sm font-medium">
                          Upload vehicle image
                        </p>
                        <p className="text-xs text-gray-400">
                          PNG, JPG up to 5MB
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Upload Button */}
                {!isReadOnly && (
                  <div className="flex justify-center">
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      disabled={isUploadingImage}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isUploadingImage}
                      className="flex items-center gap-2"
                    >
                      <Upload size={16} />
                      {imagePreview ? "Change Image" : "Upload Image"}
                    </Button>
                  </div>
                )}

                <p className="text-xs text-gray-500 text-center">
                  A high-quality image helps showcase the vehicle model to
                  potential lessees
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <DollarSign className="h-5 w-5 text-[#009639]" />
                Pricing & Terms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="weeklyRate">Weekly Rate *</Label>
                  <Input
                    id="weeklyRate"
                    type="number"
                    value={formData.weeklyRate}
                    onChange={(e) =>
                      handleInputChange("weeklyRate", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
                <div>
                  <Label htmlFor="initiationFee">Initiation Fee</Label>
                  <Input
                    id="initiationFee"
                    type="number"
                    value={formData.initiationFee}
                    onChange={(e) =>
                      handleInputChange("initiationFee", Number(e.target.value))
                    }
                    placeholder="0"
                    className="mt-1"
                    readOnly={isReadOnly}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Settings className="h-5 w-5 text-[#009639]" />
                Vehicle Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newFeature}
                  onChange={(e) => setNewFeature(e.target.value)}
                  placeholder="Add a feature..."
                  onKeyPress={(e) => e.key === "Enter" && addFeature()}
                  readOnly={isReadOnly}
                />
                <Button onClick={addFeature} size="sm" disabled={isReadOnly}>
                  <Plus size={16} />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {commonFeatures.map((feature) => (
                  <Button
                    key={feature}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!formData.features.includes(feature)) {
                        setFormData((prev) => ({
                          ...prev,
                          features: [...prev.features, feature],
                        }));
                      }
                    }}
                    disabled={formData.features.includes(feature) || isReadOnly}
                    className="text-xs"
                  >
                    {feature}
                  </Button>
                ))}
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.features.map((feature) => (
                  <Badge
                    key={feature}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {feature}
                    {!isReadOnly && (
                      <button
                        onClick={() => removeFeature(feature)}
                        className="ml-1 hover:text-red-600"
                        title={`Remove ${feature}`}
                        aria-label={`Remove ${feature}`}
                      >
                        <X size={12} />
                      </button>
                    )}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Suitable Platforms */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <CheckCircle className="h-5 w-5 text-[#009639]" />
                Suitable For Platforms
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newPlatform}
                  onChange={(e) => setNewPlatform(e.target.value)}
                  placeholder="Add a platform..."
                  onKeyPress={(e) => e.key === "Enter" && addPlatform()}
                  readOnly={isReadOnly}
                />
                <Button onClick={addPlatform} size="sm" disabled={isReadOnly}>
                  <Plus size={16} />
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {commonPlatforms.map((platform) => (
                  <Button
                    key={platform}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      if (!formData.suitableFor.includes(platform)) {
                        setFormData((prev) => ({
                          ...prev,
                          suitableFor: [...prev.suitableFor, platform],
                        }));
                      }
                    }}
                    disabled={
                      formData.suitableFor.includes(platform) || isReadOnly
                    }
                    className="text-xs"
                  >
                    {platform}
                  </Button>
                ))}
              </div>

              <div className="flex flex-wrap gap-2">
                {formData.suitableFor.map((platform) => (
                  <Badge
                    key={platform}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    {platform}
                    {!isReadOnly && (
                      <button
                        onClick={() => removePlatform(platform)}
                        className="ml-1 hover:text-red-600"
                        title={`Remove ${platform}`}
                        aria-label={`Remove ${platform}`}
                      >
                        <X size={12} />
                      </button>
                    )}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            {mode === "view" ? "Close" : "Cancel"}
          </Button>
          {mode !== "view" && (
            <Button
              onClick={handleConfirm}
              disabled={!canConfirm()}
              className="bg-[#009639] hover:bg-[#007A2F]"
            >
              <CheckCircle size={16} className="mr-2" />
              {mode === "edit" ? "Update Model" : "Add Model"}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
