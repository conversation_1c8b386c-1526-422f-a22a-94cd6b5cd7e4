"use client";

import React, { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  Car,
  DollarSign,
  Edit,
  Trash2,
  Eye,
  Settings,
  CheckCircle,
  XCircle,
  Calendar,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import VehicleCatalogFormDialog from "./components/VehicleCatalogFormDialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface VehicleCatalogItem {
  id?: string;
  make: string;
  model: string;
  year: number;
  category: "sedan" | "suv" | "hatchback" | "bakkie";
  weeklyRate: number;
  initiationFee: number;
  depositAmount: number;
  features: string[];
  suitableFor: string[];
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  description?: string;
}

export default function VehicleCatalogPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false);
  const [editingVehicle, setEditingVehicle] =
    useState<VehicleCatalogItem | null>(null);
  const [dialogMode, setDialogMode] = useState<"add" | "edit" | "view">("add");
  const [isClient, setIsClient] = useState(false);

  // Handle client-side hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Mock data - replace with actual API call
  const [catalogItems] = useState<VehicleCatalogItem[]>([
    {
      id: "1",
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      category: "sedan",
      weeklyRate: 2700,
      initiationFee: 7500,
      depositAmount: 5000,
      features: [
        "Air Conditioning",
        "Power Steering",
        "Electric Windows",
        "Radio/USB",
      ],
      suitableFor: ["Uber", "Bolt", "InDriver", "Taxify"],
      isActive: true,
      createdAt: "2024-01-15",
      updatedAt: "2024-01-20",
    },
    {
      id: "2",
      make: "Toyota",
      model: "Corolla Quest",
      year: 2022,
      category: "sedan",
      weeklyRate: 2900,
      initiationFee: 8000,
      depositAmount: 5500,
      features: ["Air Conditioning", "Power Steering", "ABS", "Airbags"],
      suitableFor: ["Uber", "Bolt", "InDriver"],
      isActive: true,
      createdAt: "2024-01-10",
      updatedAt: "2024-01-18",
    },
    {
      id: "3",
      make: "Nissan",
      model: "Almera",
      year: 2023,
      category: "sedan",
      weeklyRate: 2800,
      initiationFee: 7800,
      depositAmount: 5200,
      features: ["Air Conditioning", "Power Steering", "Electric Windows"],
      suitableFor: ["Uber", "Bolt", "Taxify"],
      isActive: false,
      createdAt: "2024-01-05",
      updatedAt: "2024-01-12",
    },
  ]);

  const filteredItems = catalogItems.filter((item) => {
    const matchesSearch =
      item.make.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.model.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory =
      filterCategory === "all" || item.category === filterCategory;
    const matchesStatus =
      filterStatus === "all" ||
      (filterStatus === "active" && item.isActive) ||
      (filterStatus === "inactive" && !item.isActive);

    return matchesSearch && matchesCategory && matchesStatus;
  });

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "sedan":
        return "bg-blue-100 text-blue-800";
      case "suv":
        return "bg-green-100 text-green-800";
      case "hatchback":
        return "bg-purple-100 text-purple-800";
      case "bakkie":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleAddVehicle = () => {
    console.log("🔴 handleAddVehicle called - opening dialog");
    console.log("🔴 Before state update:", {
      isFormDialogOpen,
      dialogMode,
      editingVehicle,
    });

    setEditingVehicle(null);
    setDialogMode("add");
    setIsFormDialogOpen(true);

    console.log("🔴 After state update called - should be:", {
      isFormDialogOpen: true,
      dialogMode: "add",
      editingVehicle: null,
    });
  };

  const handleEditVehicle = (vehicle: VehicleCatalogItem) => {
    console.log("🟡 handleEditVehicle called for vehicle:", vehicle.id);
    console.log("🟡 Before state update:", {
      isFormDialogOpen,
      dialogMode,
      editingVehicle,
    });

    // Small delay to allow dropdown to close and clean up its portal first
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("edit");
      setIsFormDialogOpen(true);
    }, 100);

    console.log("🟡 After state update called");
  };

  const handleViewVehicle = (vehicle: VehicleCatalogItem) => {
    console.log("🟢 handleViewVehicle called for vehicle:", vehicle.id);
    console.log("🟢 Before state update:", {
      isFormDialogOpen,
      dialogMode,
      editingVehicle,
    });

    // Small delay to allow dropdown to close and clean up its portal first
    setTimeout(() => {
      setEditingVehicle(vehicle);
      setDialogMode("view");
      setIsFormDialogOpen(true);
    }, 100);

    console.log("🟢 After state update called");
  };

  const handleFormSubmit = (vehicleData: VehicleCatalogItem) => {
    // TODO: Implement API call to save/update vehicle
    console.log("Vehicle data:", vehicleData);

    // Only close dialog after successful submission
    // In a real app, this would be inside a try/catch with API call
    setTimeout(() => {
      handleCloseDialog();
      // You could also show a success toast here
    }, 100);
  };

  const handleCloseDialog = () => {
    console.log("handleCloseDialog called - closing dialog");
    setIsFormDialogOpen(false);

    // Safety cleanup: Ensure body pointer events are restored
    setTimeout(() => {
      document.body.style.pointerEvents = "";
      setEditingVehicle(null);
      setDialogMode("add");
    }, 150); // Small delay to allow dialog animation to complete
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Vehicle Catalog</h1>
          <p className="text-gray-600 mt-1">
            Manage master vehicle specifications, pricing, and terms
          </p>
        </div>
        <Button
          onClick={handleAddVehicle}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Vehicle Model
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Models</p>
                <p className="text-2xl font-bold mt-1">{catalogItems.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Car className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active Models</p>
                <p className="text-2xl font-bold mt-1">
                  {catalogItems.filter((item) => item.isActive).length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Weekly Rate</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {Math.round(
                    catalogItems.reduce(
                      (sum, item) => sum + item.weeklyRate,
                      0
                    ) / catalogItems.length
                  ).toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <DollarSign className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Categories</p>
                <p className="text-2xl font-bold mt-1">
                  {new Set(catalogItems.map((item) => item.category)).size}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center">
                <Settings className="text-purple-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <CardTitle>Vehicle Models</CardTitle>
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search models..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by category"
              >
                <option value="all">All Categories</option>
                <option value="sedan">Sedan</option>
                <option value="suv">SUV</option>
                <option value="hatchback">Hatchback</option>
                <option value="bakkie">Bakkie</option>
              </select>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by status"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Vehicle</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Pricing</TableHead>
                  <TableHead>Suitable For</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {item.make} {item.model}
                        </div>
                        <div className="text-sm text-gray-500">{item.year}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getCategoryColor(item.category)}
                      >
                        {item.category.charAt(0).toUpperCase() +
                          item.category.slice(1)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          R{item.weeklyRate.toLocaleString()}/week
                        </div>
                        <div className="text-sm text-gray-500">
                          Init: R{item.initiationFee.toLocaleString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {item.suitableFor.slice(0, 2).map((platform) => (
                          <Badge
                            key={platform}
                            variant="secondary"
                            className="text-xs"
                          >
                            {platform}
                          </Badge>
                        ))}
                        {item.suitableFor.length > 2 && (
                          <Badge variant="secondary" className="text-xs">
                            +{item.suitableFor.length - 2}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={
                          item.isActive
                            ? "bg-green-100 text-green-800 border-green-200"
                            : "bg-red-100 text-red-800 border-red-200"
                        }
                      >
                        {item.isActive ? (
                          <>
                            <CheckCircle size={12} className="mr-1" />
                            Active
                          </>
                        ) : (
                          <>
                            <XCircle size={12} className="mr-1" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar size={14} className="mr-1" />
                        <span>
                          {isClient && item.updatedAt
                            ? new Date(item.updatedAt).toLocaleDateString(
                                "en-GB"
                              )
                            : isClient
                              ? "N/A"
                              : "Loading..."}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Settings size={16} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewVehicle(item)}
                          >
                            <Eye size={14} className="mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEditVehicle(item)}
                          >
                            <Edit size={14} className="mr-2" />
                            Edit Model
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 size={14} className="mr-2" />
                            Delete Model
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Catalog Form Dialog */}
      <VehicleCatalogFormDialog
        isOpen={isFormDialogOpen}
        onClose={handleCloseDialog}
        onConfirm={handleFormSubmit}
        editingVehicle={editingVehicle}
        mode={dialogMode}
      />
    </div>
  );
}
