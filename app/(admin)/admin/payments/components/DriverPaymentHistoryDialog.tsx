"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  User,
  Car,
  DollarSign,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  CreditCard,
  FileText,
} from "lucide-react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";

interface PaymentRecord {
  id: string;
  paymentType: "weekly_lease" | "initiation_fee" | "late_fee" | "maintenance";
  amount: number;
  dueDate: string;
  paidDate?: string;
  status: "paid" | "overdue" | "pending" | "partial";
  paymentMethod?: "bank_transfer" | "cash" | "card";
  reference?: string;
  lateFee?: number;
  notes?: string;
}

interface DriverInfo {
  id: string;
  name: string;
  email: string;
  phone: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  weeklyRate: number;
  totalPaid: number;
  outstandingBalance: number;
  paymentScore: number;
  onTimePayments: number;
  totalPayments: number;
}

interface DriverPaymentHistoryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  driverInfo: DriverInfo | null;
  paymentHistory: PaymentRecord[];
}

export default function DriverPaymentHistoryDialog({
  isOpen,
  onClose,
  driverInfo,
  paymentHistory,
}: DriverPaymentHistoryDialogProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  if (!driverInfo) return null;

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const getStatusColor = (status: PaymentRecord["status"]) => {
    switch (status) {
      case "paid":
        return "bg-green-100 text-green-800 border-green-200";
      case "pending":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "overdue":
        return "bg-red-100 text-red-800 border-red-200";
      case "partial":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: PaymentRecord["status"]) => {
    switch (status) {
      case "paid":
        return <CheckCircle size={14} />;
      case "pending":
        return <Clock size={14} />;
      case "overdue":
        return <AlertTriangle size={14} />;
      case "partial":
        return <Clock size={14} />;
      default:
        return <FileText size={14} />;
    }
  };

  const getPaymentTypeLabel = (type: PaymentRecord["paymentType"]) => {
    switch (type) {
      case "weekly_lease":
        return "Weekly Lease";
      case "initiation_fee":
        return "Initiation Fee";
      case "late_fee":
        return "Late Fee";
      case "maintenance":
        return "Maintenance";
      default:
        return type;
    }
  };

  const recentPayments = paymentHistory
    .filter(p => p.status === "paid")
    .sort((a, b) => new Date(b.paidDate!).getTime() - new Date(a.paidDate!).getTime())
    .slice(0, 5);

  const overduePayments = paymentHistory.filter(p => p.status === "overdue");
  const pendingPayments = paymentHistory.filter(p => p.status === "pending");

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <User className="h-5 w-5 text-[#009639]" />
            Payment History - {driverInfo.name}
          </DialogTitle>
          <DialogDescription>
            Comprehensive payment history and financial overview for this driver
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="history">Payment History</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Driver Info Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User size={20} className="text-[#009639]" />
                  Driver Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500">Driver Name</p>
                      <p className="font-medium">{driverInfo.name}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Email</p>
                      <p className="font-medium">{driverInfo.email}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Phone</p>
                      <p className="font-medium">{driverInfo.phone}</p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500">Vehicle</p>
                      <p className="font-medium flex items-center gap-2">
                        <Car size={16} className="text-gray-400" />
                        {driverInfo.vehicleName}
                      </p>
                      <p className="text-sm text-gray-500">{driverInfo.vehicleRegistration}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Assignment Date</p>
                      <p className="font-medium">
                        {new Date(driverInfo.assignmentDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Financial Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Weekly Rate</p>
                      <p className="text-2xl font-bold text-[#009639]">
                        R{driverInfo.weeklyRate.toLocaleString()}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-[#009639]" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Total Paid</p>
                      <p className="text-2xl font-bold text-green-600">
                        R{driverInfo.totalPaid.toLocaleString()}
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Outstanding</p>
                      <p className={`text-2xl font-bold ${
                        driverInfo.outstandingBalance > 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        R{driverInfo.outstandingBalance.toLocaleString()}
                      </p>
                    </div>
                    <AlertTriangle className={`h-8 w-8 ${
                      driverInfo.outstandingBalance > 0 ? 'text-red-600' : 'text-gray-400'
                    }`} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Payment Score</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {driverInfo.paymentScore}%
                      </p>
                      <p className="text-xs text-gray-500">
                        {driverInfo.onTimePayments}/{driverInfo.totalPayments} on time
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Payments */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle size={18} className="text-green-600" />
                    Recent Payments
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {recentPayments.length > 0 ? (
                    <div className="space-y-3">
                      {recentPayments.map((payment) => (
                        <div
                          key={payment.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div>
                            <p className="font-medium">{getPaymentTypeLabel(payment.paymentType)}</p>
                            <p className="text-sm text-gray-500">
                              {new Date(payment.paidDate!).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-green-600">
                              R{payment.amount.toLocaleString()}
                            </p>
                            {payment.paymentMethod && (
                              <p className="text-xs text-gray-500 capitalize">
                                {payment.paymentMethod.replace('_', ' ')}
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No recent payments</p>
                  )}
                </CardContent>
              </Card>

              {/* Outstanding Items */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle size={18} className="text-red-600" />
                    Outstanding Items
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {[...overduePayments, ...pendingPayments].length > 0 ? (
                    <div className="space-y-3">
                      {[...overduePayments, ...pendingPayments].map((payment) => (
                        <div
                          key={payment.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div>
                            <p className="font-medium">{getPaymentTypeLabel(payment.paymentType)}</p>
                            <p className="text-sm text-gray-500">
                              Due: {new Date(payment.dueDate).toLocaleDateString()}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-red-600">
                              R{payment.amount.toLocaleString()}
                            </p>
                            <Badge variant="outline" className={getStatusColor(payment.status)}>
                              {getStatusIcon(payment.status)}
                              <span className="ml-1 capitalize">{payment.status}</span>
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 text-center py-4">No outstanding payments</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Complete Payment History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Payment Type</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Due Date</TableHead>
                        <TableHead>Paid Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Method</TableHead>
                        <TableHead>Reference</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {paymentHistory.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-medium">
                            {getPaymentTypeLabel(payment.paymentType)}
                          </TableCell>
                          <TableCell>
                            <span className="font-bold">
                              R{payment.amount.toLocaleString()}
                            </span>
                            {payment.lateFee && payment.lateFee > 0 && (
                              <div className="text-xs text-red-600">
                                +R{payment.lateFee} late fee
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {new Date(payment.dueDate).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {payment.paidDate
                              ? new Date(payment.paidDate).toLocaleDateString()
                              : "-"}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className={getStatusColor(payment.status)}>
                              {getStatusIcon(payment.status)}
                              <span className="ml-1 capitalize">{payment.status}</span>
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {payment.paymentMethod ? (
                              <div className="flex items-center gap-1">
                                <CreditCard size={14} />
                                <span className="capitalize">
                                  {payment.paymentMethod.replace('_', ' ')}
                                </span>
                              </div>
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {payment.reference || "-"}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>On-time Payments</span>
                      <span className="font-bold text-green-600">
                        {driverInfo.onTimePayments} / {driverInfo.totalPayments}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Payment Score</span>
                      <span className="font-bold text-blue-600">{driverInfo.paymentScore}%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Late Payments</span>
                      <span className="font-bold text-red-600">
                        {driverInfo.totalPayments - driverInfo.onTimePayments}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Financial Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Paid</span>
                      <span className="font-bold text-green-600">
                        R{driverInfo.totalPaid.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Outstanding Balance</span>
                      <span className={`font-bold ${
                        driverInfo.outstandingBalance > 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        R{driverInfo.outstandingBalance.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Weekly Rate</span>
                      <span className="font-bold text-[#009639]">
                        R{driverInfo.weeklyRate.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
