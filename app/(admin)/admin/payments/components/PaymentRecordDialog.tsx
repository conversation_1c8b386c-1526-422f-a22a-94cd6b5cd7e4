"use client";

import React, { useState, useEffect } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  DollarSign,
  User,
  Car,
  Calendar,
  CreditCard,
  FileText,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Assignment {
  id: string;
  driverName: string;
  vehicleName: string;
  vehicleRegistration: string;
  weeklyRate: number;
  outstandingBalance: number;
}

interface PaymentRecordDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (paymentData: any) => void;
  assignments: Assignment[];
  selectedAssignmentId?: string;
}

export default function PaymentRecordDialog({
  isOpen,
  onClose,
  onConfirm,
  assignments,
  selectedAssignmentId,
}: PaymentRecordDialogProps) {
  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const [selectedAssignment, setSelectedAssignment] =
    useState<Assignment | null>(
      selectedAssignmentId
        ? assignments.find((a) => a.id === selectedAssignmentId) || null
        : null
    );
  const [paymentType, setPaymentType] = useState<string>("");
  const [amount, setAmount] = useState<number>(0);
  const [paymentDate, setPaymentDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );
  const [paymentMethod, setPaymentMethod] = useState<string>("");
  const [reference, setReference] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [applyLateFee, setApplyLateFee] = useState<boolean>(false);
  const [lateFeeAmount, setLateFeeAmount] = useState<number>(0);

  const handleAssignmentSelect = (assignmentId: string) => {
    const assignment = assignments.find((a) => a.id === assignmentId);
    setSelectedAssignment(assignment || null);

    // Auto-set amount for weekly lease payments
    if (assignment && paymentType === "weekly_lease") {
      setAmount(assignment.weeklyRate);
    }
  };

  const handlePaymentTypeChange = (type: string) => {
    setPaymentType(type);

    // Auto-set amount based on payment type
    if (selectedAssignment) {
      switch (type) {
        case "weekly_lease":
          setAmount(selectedAssignment.weeklyRate);
          break;
        case "outstanding_balance":
          setAmount(selectedAssignment.outstandingBalance);
          break;
        default:
          setAmount(0);
      }
    }
  };

  const handleConfirm = () => {
    if (selectedAssignment && paymentType && amount > 0) {
      const paymentData = {
        assignmentId: selectedAssignment.id,
        paymentType,
        amount,
        paymentDate,
        paymentMethod,
        reference,
        notes,
        lateFee: applyLateFee ? lateFeeAmount : 0,
      };
      onConfirm(paymentData);
      // Don't call onClose or resetForm here - let the parent handle it after successful submission
    }
  };

  const resetForm = () => {
    if (!selectedAssignmentId) {
      setSelectedAssignment(null);
    }
    setPaymentType("");
    setAmount(0);
    setPaymentMethod("");
    setReference("");
    setNotes("");
    setApplyLateFee(false);
    setLateFeeAmount(0);
    setPaymentDate(new Date().toISOString().split("T")[0]);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
      onClose();
    }
  };

  const canConfirm = () => {
    return (
      selectedAssignment &&
      paymentType &&
      amount > 0 &&
      paymentDate &&
      paymentMethod
    );
  };

  const totalAmount = amount + (applyLateFee ? lateFeeAmount : 0);

  const getPaymentTypeOptions = () => {
    const options = [
      { value: "weekly_lease", label: "Weekly Lease Payment" },
      { value: "initiation_fee", label: "Initiation Fee" },
      { value: "late_fee", label: "Late Fee" },
      { value: "maintenance", label: "Maintenance Fee" },
      { value: "other", label: "Other" },
    ];

    if (selectedAssignment && selectedAssignment.outstandingBalance > 0) {
      options.splice(2, 0, {
        value: "outstanding_balance",
        label: "Outstanding Balance",
      });
    }

    return options;
  };

  // Add useEffect to reset form when dialog closes
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // Initialize selected assignment on mount if provided
  useEffect(() => {
    if (selectedAssignmentId && isOpen) {
      const assignment = assignments.find((a) => a.id === selectedAssignmentId);
      setSelectedAssignment(assignment || null);
    }
  }, [selectedAssignmentId, assignments, isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <DollarSign className="h-5 w-5 text-[#009639]" />
            Record Payment
          </DialogTitle>
          <DialogDescription>
            Record a payment received from a driver for their vehicle lease
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Assignment Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <User className="h-5 w-5 text-[#009639]" />
                Select Assignment
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                onValueChange={handleAssignmentSelect}
                value={selectedAssignment?.id || ""}
                disabled={!!selectedAssignmentId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose an assignment..." />
                </SelectTrigger>
                <SelectContent>
                  {assignments.map((assignment) => (
                    <SelectItem key={assignment.id} value={assignment.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {assignment.driverName}
                        </span>
                        <span className="text-sm text-gray-500">
                          {assignment.vehicleName} •{" "}
                          {assignment.vehicleRegistration}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedAssignment && (
                <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium flex items-center gap-2">
                        <User size={16} className="text-gray-500" />
                        {selectedAssignment.driverName}
                      </h4>
                      <p className="text-sm text-gray-600 flex items-center gap-2 mt-1">
                        <Car size={16} className="text-gray-500" />
                        {selectedAssignment.vehicleName} •{" "}
                        {selectedAssignment.vehicleRegistration}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">Weekly Rate</p>
                      <p className="font-bold text-[#009639]">
                        R{selectedAssignment.weeklyRate.toLocaleString()}
                      </p>
                      {selectedAssignment.outstandingBalance > 0 && (
                        <p className="text-sm text-red-600 mt-1">
                          Outstanding: R
                          {selectedAssignment.outstandingBalance.toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Details */}
          {selectedAssignment && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <DollarSign className="h-5 w-5 text-[#009639]" />
                  Payment Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="paymentType">Payment Type</Label>
                    <Select
                      onValueChange={handlePaymentTypeChange}
                      value={paymentType}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select payment type..." />
                      </SelectTrigger>
                      <SelectContent>
                        {getPaymentTypeOptions().map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="amount">Amount</Label>
                    <Input
                      id="amount"
                      type="number"
                      value={amount}
                      onChange={(e) => setAmount(Number(e.target.value))}
                      placeholder="0.00"
                      className="mt-1"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="paymentDate">Payment Date</Label>
                    <Input
                      id="paymentDate"
                      type="date"
                      value={paymentDate}
                      onChange={(e) => setPaymentDate(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="paymentMethod">Payment Method</Label>
                    <Select
                      onValueChange={setPaymentMethod}
                      value={paymentMethod}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select method..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bank_transfer">
                          Bank Transfer
                        </SelectItem>
                        <SelectItem value="cash">Cash</SelectItem>
                        <SelectItem value="card">Card Payment</SelectItem>
                        <SelectItem value="eft">EFT</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="reference">Payment Reference</Label>
                  <Input
                    id="reference"
                    value={reference}
                    onChange={(e) => setReference(e.target.value)}
                    placeholder="Transaction reference or receipt number"
                    className="mt-1"
                  />
                </div>

                {/* Late Fee Section */}
                <div className="border-t pt-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <Input
                      type="checkbox"
                      id="applyLateFee"
                      checked={applyLateFee}
                      onChange={(e) => setApplyLateFee(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <Label htmlFor="applyLateFee" className="text-sm">
                      Apply late fee
                    </Label>
                  </div>

                  {applyLateFee && (
                    <div>
                      <Label htmlFor="lateFeeAmount">Late Fee Amount</Label>
                      <Input
                        id="lateFeeAmount"
                        type="number"
                        value={lateFeeAmount}
                        onChange={(e) =>
                          setLateFeeAmount(Number(e.target.value))
                        }
                        placeholder="0.00"
                        className="mt-1"
                      />
                    </div>
                  )}
                </div>

                <div>
                  <Label htmlFor="notes">Payment Notes</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Any additional notes about this payment..."
                    className="mt-1"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {/* Payment Summary */}
          {selectedAssignment && paymentType && amount > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <CheckCircle className="h-5 w-5 text-[#009639]" />
                  Payment Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment Type:</span>
                    <span className="font-medium">
                      {
                        getPaymentTypeOptions().find(
                          (opt) => opt.value === paymentType
                        )?.label
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base Amount:</span>
                    <span className="font-medium">
                      R{amount.toLocaleString()}
                    </span>
                  </div>
                  {applyLateFee && lateFeeAmount > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Late Fee:</span>
                      <span className="font-medium text-red-600">
                        R{lateFeeAmount.toLocaleString()}
                      </span>
                    </div>
                  )}
                  <div className="border-t pt-3">
                    <div className="flex justify-between">
                      <span className="text-lg font-medium">Total Amount:</span>
                      <span className="text-lg font-bold text-[#009639]">
                        R{totalAmount.toLocaleString()}
                      </span>
                    </div>
                  </div>

                  {paymentType === "outstanding_balance" &&
                    amount < selectedAssignment.outstandingBalance && (
                      <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center gap-2">
                          <AlertTriangle
                            size={16}
                            className="text-yellow-600"
                          />
                          <span className="text-sm text-yellow-800">
                            Partial payment: R
                            {(
                              selectedAssignment.outstandingBalance - amount
                            ).toLocaleString()}{" "}
                            will remain outstanding
                          </span>
                        </div>
                      </div>
                    )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!canConfirm()}
            className="bg-[#009639] hover:bg-[#007A2F]"
          >
            <CheckCircle size={16} className="mr-2" />
            Record Payment
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
