"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  ChevronDown,
  MoreVertical,
  Car,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  Download,
  Mail,
  TrendingUp,
  MapPin,
} from "lucide-react";
import Link from "next/link";
import { Card, CardContent, CardHeader } from "@/components/ui/card";

interface VehicleListing {
  id: string;
  ownerName: string;
  ownerEmail: string;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  submittedDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  listingType: "rental" | "co_ownership" | "lease_to_own";
  askingPrice: number;
  fractionOffer?: number;
  condition: "new" | "used";
  mileage?: number;
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
  images: string[];
  location: string;
}

export default function VehicleListingsPage() {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [filterType, setFilterType] = useState<
    "all" | "rental" | "co_ownership" | "lease_to_own"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual data fetching
  const [listings] = useState<VehicleListing[]>([
    {
      id: "1",
      ownerName: "Lisa Johnson",
      ownerEmail: "<EMAIL>",
      vehicleName: "BMW X3 2021",
      make: "BMW",
      model: "X3",
      year: 2021,
      submittedDate: "2024-01-15",
      status: "pending",
      listingType: "co_ownership",
      askingPrice: 450000,
      fractionOffer: 0.25,
      condition: "used",
      mileage: 45000,
      location: "Cape Town",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: false },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: false },
      ],
      images: ["/images/cars/bmw-x3.jpg"],
    },
    {
      id: "2",
      ownerName: "Michael Chen",
      ownerEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2022",
      make: "Toyota",
      model: "Corolla",
      year: 2022,
      submittedDate: "2024-01-14",
      status: "under_review",
      listingType: "rental",
      askingPrice: 2800,
      condition: "used",
      mileage: 25000,
      location: "Johannesburg",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
      images: ["/images/cars/toyota-corolla.jpg"],
    },
    {
      id: "3",
      ownerName: "Sarah Williams",
      ownerEmail: "<EMAIL>",
      vehicleName: "Honda Civic 2023",
      make: "Honda",
      model: "Civic",
      year: 2023,
      submittedDate: "2024-01-10",
      status: "approved",
      decisionDate: "2024-01-12",
      listingType: "rental",
      askingPrice: 3200,
      condition: "new",
      mileage: 5000,
      location: "Durban",
      documents: [
        { name: "Vehicle Registration", uploaded: true, verified: true },
        { name: "Insurance Certificate", uploaded: true, verified: true },
        { name: "Vehicle Photos", uploaded: true, verified: true },
        { name: "Service History", uploaded: true, verified: true },
      ],
      images: ["/images/cars/honda-civic.jpg"],
    },
  ]);

  // Helper functions
  const getStatusColor = (status: VehicleListing["status"]) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "under_review":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "approved":
        return "text-green-600 bg-green-50 border-green-200";
      case "rejected":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const getStatusIcon = (status: VehicleListing["status"]) => {
    switch (status) {
      case "pending":
        return <Clock size={12} />;
      case "under_review":
        return <Eye size={12} />;
      case "approved":
        return <CheckCircle size={12} />;
      case "rejected":
        return <XCircle size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  const getTabCount = (tab: "active" | "history") => {
    if (tab === "active") {
      return listings.filter(
        (listing) =>
          listing.status === "pending" || listing.status === "under_review"
      ).length;
    }
    return listings.filter(
      (listing) =>
        listing.status === "approved" || listing.status === "rejected"
    ).length;
  };

  const getFilterCount = (status: typeof filterStatus) => {
    if (status === "all") return listings.length;
    return listings.filter((listing) => listing.status === status).length;
  };

  // Filter listings based on current tab
  const activeListings = listings.filter(
    (listing) =>
      listing.status === "pending" || listing.status === "under_review"
  );

  const historicalListings = listings.filter(
    (listing) => listing.status === "approved" || listing.status === "rejected"
  );

  const currentListings =
    currentTab === "active" ? activeListings : historicalListings;

  const filteredListings = currentListings.filter((listing) => {
    const matchesStatusFilter =
      filterStatus === "all" || listing.status === filterStatus;
    const matchesTypeFilter =
      filterType === "all" || listing.listingType === filterType;
    const matchesSearch =
      listing.ownerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      listing.vehicleName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      listing.location.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesStatusFilter && matchesTypeFilter && matchesSearch;
  });

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "co_ownership":
        return "Co-ownership";
      case "lease_to_own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            Vehicle Listing Management
          </h1>
          <p className="text-gray-600 mt-1">
            Review and approve vehicle listings from EARN users
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" className="flex items-center gap-2">
            <Download size={16} />
            Export Data
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Filter size={16} />
            Advanced Filters
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Listings</p>
                <p className="text-2xl font-bold mt-1">{listings.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Car className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Review</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    listings.filter(
                      (listing) =>
                        listing.status === "pending" ||
                        listing.status === "under_review"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approved</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    listings.filter((listing) => listing.status === "approved")
                      .length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Approval Rate</p>
                <p className="text-2xl font-bold mt-1">
                  {Math.round(
                    (listings.filter((listing) => listing.status === "approved")
                      .length /
                      listings.filter(
                        (listing) =>
                          listing.status === "approved" ||
                          listing.status === "rejected"
                      ).length) *
                      100
                  )}
                  %
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <TrendingUp className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs
              value={currentTab}
              onValueChange={(value) =>
                setCurrentTab(value as "active" | "history")
              }
            >
              <TabsList>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <Clock size={16} />
                  Active Listings ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="history"
                  className="flex items-center gap-2"
                >
                  <Calendar size={16} />
                  Listing History ({getTabCount("history")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Search
                  size={16}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search listings..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-[#009639] focus:border-transparent w-64"
                />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Type:{" "}
                    {filterType === "all"
                      ? "All"
                      : getListingTypeLabel(filterType)}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Type</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Types" },
                    { key: "rental", label: "Rental" },
                    { key: "co_ownership", label: "Co-ownership" },
                    { key: "lease_to_own", label: "Lease-to-Own" },
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterType(filter.key as typeof filterType)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {
                          listings.filter(
                            (listing) =>
                              filter.key === "all" ||
                              listing.listingType === filter.key
                          ).length
                        }
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Filter size={16} />
                    Status:{" "}
                    {filterStatus === "all"
                      ? "All"
                      : filterStatus.replace("_", " ")}
                    <ChevronDown size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { key: "all", label: "All Listings" },
                    ...(currentTab === "active"
                      ? [
                          { key: "pending", label: "Pending" },
                          { key: "under_review", label: "Under Review" },
                        ]
                      : [
                          { key: "approved", label: "Approved" },
                          { key: "rejected", label: "Rejected" },
                        ]),
                  ].map((filter) => (
                    <DropdownMenuItem
                      key={filter.key}
                      onClick={() =>
                        setFilterStatus(filter.key as typeof filterStatus)
                      }
                      className="flex items-center justify-between"
                    >
                      <span>{filter.label}</span>
                      <Badge variant="secondary" className="ml-2">
                        {getFilterCount(filter.key as typeof filterStatus)}
                      </Badge>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {filteredListings.length === 0 ? (
            <div className="text-center py-12">
              <Car size={48} className="mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">
                No Listings Found
              </h3>
              <p className="text-sm text-gray-500">
                {searchQuery
                  ? "Try adjusting your search criteria"
                  : "No listings match the selected filter"}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Owner</TableHead>
                    <TableHead>Vehicle</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredListings.map((listing) => (
                    <TableRow key={listing.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div>
                          <div className="font-medium text-gray-900">
                            {listing.ownerName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {listing.ownerEmail}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Car size={16} className="text-gray-400 mr-2" />
                          <span className="text-sm font-medium">
                            {listing.vehicleName}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {getListingTypeLabel(listing.listingType)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          R{listing.askingPrice.toLocaleString()}
                        </div>
                        {listing.fractionOffer && (
                          <div className="text-xs text-gray-500">
                            {(listing.fractionOffer * 100).toFixed(0)}% fraction
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin size={14} className="text-gray-400 mr-1" />
                          <span className="text-sm">{listing.location}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(listing.submittedDate).toLocaleDateString(
                            "en-GB",
                            {
                              day: "2-digit",
                              month: "2-digit",
                              year: "numeric",
                            }
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={`flex items-center gap-1 w-fit ${getStatusColor(listing.status)}`}
                        >
                          {getStatusIcon(listing.status)}
                          <span className="capitalize">
                            {listing.status.replace("_", " ")}
                          </span>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <span className="text-green-600">
                            {listing.documents.filter((d) => d.uploaded).length}
                          </span>
                          <span className="text-gray-400">
                            /{listing.documents.length}
                          </span>
                          <span className="text-gray-500 ml-1">uploaded</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical size={16} />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/admin/listings/${listing.id}`}>
                                  <Eye size={14} className="mr-2" />
                                  View Details
                                </Link>
                              </DropdownMenuItem>
                              {currentTab === "active" && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-green-600">
                                    <CheckCircle size={14} className="mr-2" />
                                    Quick Approve
                                  </DropdownMenuItem>
                                  <DropdownMenuItem className="text-red-600">
                                    <XCircle size={14} className="mr-2" />
                                    Quick Reject
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuSeparator />
                              <DropdownMenuItem>
                                <Download size={14} className="mr-2" />
                                Download Documents
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Mail size={14} className="mr-2" />
                                Contact Owner
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
