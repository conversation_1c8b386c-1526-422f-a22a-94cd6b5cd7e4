"use client";

import React, { useState } from "react";
import {
  ArrowLeft,
  User,
  Car,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Eye,
  Clock,
  DollarSign,
  MapPin,
  Calendar,
  AlertTriangle,
} from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface VehicleListing {
  id: string;
  ownerName: string;
  ownerEmail: string;
  ownerPhone: string;
  vehicleName: string;
  make: string;
  model: string;
  year: number;
  submittedDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  listingType: "rental" | "co_ownership" | "lease_to_own";
  askingPrice: number;
  fractionOffer?: number;
  condition: "new" | "used";
  mileage?: number;
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean | "failed";
    fileUrl?: string;
  }[];
  images: string[];
  location: string;
  description?: string;
}

export default function ListingReviewPageClient({
  listingId,
}: {
  listingId: string;
}) {
  const [currentTab, setCurrentTab] = useState("overview");
  const [decisionReason, setDecisionReason] = useState("");
  const [isDecisionDialogOpen, setIsDecisionDialogOpen] = useState(false);
  const [pendingDecision, setPendingDecision] = useState<
    "approved" | "rejected" | null
  >(null);
  const [documentStatuses, setDocumentStatuses] = useState<
    Record<string, boolean | "failed">
  >({});

  // Mock data - replace with actual data fetching
  const listing: VehicleListing = {
    id: listingId,
    ownerName: "Lisa Johnson",
    ownerEmail: "<EMAIL>",
    ownerPhone: "+27 82 123 4567",
    vehicleName: "BMW X3 2021",
    make: "BMW",
    model: "X3",
    year: 2021,
    submittedDate: "2024-01-15T10:30:00Z",
    status: "pending",
    listingType: "co_ownership",
    askingPrice: 450000,
    fractionOffer: 0.25,
    condition: "used",
    mileage: 45000,
    location: "Cape Town, Western Cape",
    description: "Well-maintained BMW X3 in excellent condition. Regular service history, no accidents. Perfect for co-ownership arrangement.",
    documents: [
      {
        name: "Vehicle Registration",
        uploaded: true,
        verified: false,
        fileUrl: "/docs/registration.pdf",
      },
      {
        name: "Insurance Certificate",
        uploaded: true,
        verified: true,
        fileUrl: "/docs/insurance.pdf",
      },
      {
        name: "Vehicle Photos",
        uploaded: true,
        verified: true,
        fileUrl: "/docs/photos.zip",
      },
      {
        name: "Service History",
        uploaded: true,
        verified: false,
        fileUrl: "/docs/service.pdf",
      },
    ],
    images: ["/images/cars/bmw-x3-1.jpg", "/images/cars/bmw-x3-2.jpg"],
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusColor = (status: VehicleListing["status"]) => {
    switch (status) {
      case "pending":
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case "under_review":
        return "text-blue-600 bg-blue-50 border-blue-200";
      case "approved":
        return "text-green-600 bg-green-50 border-green-200";
      case "rejected":
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    setPendingDecision(decision);
    setIsDecisionDialogOpen(true);
  };

  const confirmDecision = () => {
    if (pendingDecision) {
      console.log(`Listing ${listing.id} ${pendingDecision}`, decisionReason);
      // Here you would typically update the listing status in your backend
      setIsDecisionDialogOpen(false);
      setPendingDecision(null);
      setDecisionReason("");
    }
  };

  const handleDocumentVerification = (docName: string, status: boolean | "failed") => {
    setDocumentStatuses(prev => ({
      ...prev,
      [docName]: status
    }));
  };

  const getListingTypeLabel = (type: string) => {
    switch (type) {
      case "rental":
        return "Rental";
      case "co_ownership":
        return "Co-ownership";
      case "lease_to_own":
        return "Lease-to-Own";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/listings">
              <ArrowLeft size={16} />
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              Listing Review
            </h1>
            <p className="text-gray-600 mt-1">
              {listing.ownerName} • {listing.vehicleName}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Badge
            variant="outline"
            className={`${getStatusColor(listing.status)} px-3 py-1`}
          >
            <span className="capitalize">
              {listing.status.replace("_", " ")}
            </span>
          </Badge>
          {listing.status === "pending" ||
          listing.status === "under_review" ? (
            <div className="flex space-x-2">
              <Button
                onClick={() => handleDecision("approved")}
                className="bg-[#009639] hover:bg-[#007A2F]"
              >
                <CheckCircle size={16} className="mr-2" />
                Approve Listing
              </Button>
              <Button
                variant="outline"
                onClick={() => handleDecision("rejected")}
                className="border-red-600 text-red-600 hover:bg-red-50"
              >
                <XCircle size={16} className="mr-2" />
                Reject Listing
              </Button>
            </div>
          ) : null}
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <Tabs value={currentTab} onValueChange={setCurrentTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
              <TabsTrigger value="decision">Decision</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Vehicle Owner Profile */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    Vehicle Owner Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Full Name
                      </Label>
                      <p className="text-lg font-medium">
                        {listing.ownerName}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Submission Date
                      </Label>
                      <p className="text-lg">
                        {formatDate(listing.submittedDate)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Email Address
                      </Label>
                      <p className="text-lg">{listing.ownerEmail}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Phone Number
                      </Label>
                      <p className="text-lg">{listing.ownerPhone}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Vehicle Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Car size={20} className="text-[#009639]" />
                    Vehicle Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Vehicle
                      </Label>
                      <p className="text-lg font-medium">
                        {listing.make} {listing.model} {listing.year}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Listing Type
                      </Label>
                      <p className="text-lg">
                        {getListingTypeLabel(listing.listingType)}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Condition
                      </Label>
                      <p className="text-lg capitalize">{listing.condition}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Mileage
                      </Label>
                      <p className="text-lg">
                        {listing.mileage?.toLocaleString()} km
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Location
                      </Label>
                      <p className="text-lg">{listing.location}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Asking Price
                      </Label>
                      <p className="text-lg font-medium">
                        R{listing.askingPrice.toLocaleString()}
                        {listing.fractionOffer && (
                          <span className="text-sm text-gray-500 ml-2">
                            ({(listing.fractionOffer * 100).toFixed(0)}% fraction)
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  {listing.description && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">
                        Description
                      </Label>
                      <p className="text-sm text-gray-700 mt-1">
                        {listing.description}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
