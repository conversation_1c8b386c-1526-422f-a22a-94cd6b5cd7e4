"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  User,
  Car,
  DollarSign,
  Calendar,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Star,
  Wrench,
  FileText,
  Phone,
  Mail,
  MapPin,
  Activity,
  BarChart3,
} from "lucide-react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";

interface DriverPerformance {
  id: string;
  driverName: string;
  driverEmail: string;
  driverPhone?: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "active" | "warning" | "terminated";

  // Payment Performance
  paymentScore: number;
  totalPayments: number;
  onTimePayments: number;
  latePayments: number;
  missedPayments: number;
  totalOutstanding: number;

  // Vehicle Care
  vehicleConditionScore: number;
  maintenanceRequests: number;
  damageReports: number;
  lastInspectionDate: string;
  lastInspectionScore: number;

  // Platform Performance
  platformRating?: number;
  totalTrips?: number;
  monthlyEarnings?: number;

  // Compliance
  documentsUpToDate: boolean;
  insuranceCurrent: boolean;
  licenseValid: boolean;
  activeDisputes: number;
  resolvedDisputes: number;
  warnings: number;
  lastContactDate: string;
  notes?: string;
}

interface PerformanceMetric {
  date: string;
  paymentScore: number;
  vehicleScore: number;
  platformRating: number;
  trips: number;
  earnings: number;
}

interface DriverPerformanceDetailDialogProps {
  isOpen: boolean;
  onClose: () => void;
  driver: DriverPerformance | null;
}

export default function DriverPerformanceDetailDialog({
  isOpen,
  onClose,
  driver,
}: DriverPerformanceDetailDialogProps) {
  const [activeTab, setActiveTab] = useState("overview");

  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  if (!driver) return null;

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  // Mock historical performance data
  const performanceHistory: PerformanceMetric[] = [
    {
      date: "2024-01-01",
      paymentScore: 85,
      vehicleScore: 80,
      platformRating: 4.5,
      trips: 45,
      earnings: 12500,
    },
    {
      date: "2024-01-08",
      paymentScore: 90,
      vehicleScore: 85,
      platformRating: 4.6,
      trips: 52,
      earnings: 14200,
    },
    {
      date: "2024-01-15",
      paymentScore: 95,
      vehicleScore: 88,
      platformRating: 4.8,
      trips: 58,
      earnings: 16800,
    },
    {
      date: "2024-01-22",
      paymentScore: driver.paymentScore,
      vehicleScore: driver.vehicleConditionScore,
      platformRating: driver.platformRating || 0,
      trips: Math.floor((driver.totalTrips || 0) / 4),
      earnings: Math.floor((driver.monthlyEarnings || 0) / 4),
    },
  ];

  const getStatusColor = (status: DriverPerformance["status"]) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "warning":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 75) return "text-yellow-600";
    return "text-red-600";
  };

  const getScoreBadgeColor = (score: number) => {
    if (score >= 90) return "bg-green-100 text-green-800 border-green-200";
    if (score >= 75) return "bg-yellow-100 text-yellow-800 border-yellow-200";
    return "bg-red-100 text-red-800 border-red-200";
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl flex items-center gap-2">
            <User className="h-5 w-5 text-[#009639]" />
            Performance Report - {driver.driverName}
          </DialogTitle>
          <DialogDescription>
            Comprehensive performance analysis and detailed metrics for this driver
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="compliance">Compliance</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Driver Info Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <User size={20} className="text-[#009639]" />
                    Driver Information
                  </div>
                  <Badge variant="outline" className={getStatusColor(driver.status)}>
                    {driver.status.charAt(0).toUpperCase() + driver.status.slice(1)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500">Driver Name</p>
                      <p className="font-medium">{driver.driverName}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 flex items-center gap-1">
                        <Mail size={12} />
                        Email
                      </p>
                      <p className="font-medium">{driver.driverEmail}</p>
                    </div>
                    {driver.driverPhone && (
                      <div>
                        <p className="text-sm text-gray-500 flex items-center gap-1">
                          <Phone size={12} />
                          Phone
                        </p>
                        <p className="font-medium">{driver.driverPhone}</p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500 flex items-center gap-1">
                        <Car size={12} />
                        Vehicle
                      </p>
                      <p className="font-medium">{driver.vehicleName}</p>
                      <p className="text-sm text-gray-500">{driver.vehicleRegistration}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 flex items-center gap-1">
                        <Calendar size={12} />
                        Assignment Date
                      </p>
                      <p className="font-medium">
                        {new Date(driver.assignmentDate).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div>
                      <p className="text-sm text-gray-500 flex items-center gap-1">
                        <Activity size={12} />
                        Last Contact
                      </p>
                      <p className="font-medium">
                        {new Date(driver.lastContactDate).toLocaleDateString()}
                      </p>
                    </div>
                    {driver.notes && (
                      <div>
                        <p className="text-sm text-gray-500 flex items-center gap-1">
                          <FileText size={12} />
                          Notes
                        </p>
                        <p className="text-sm">{driver.notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Payment Score</p>
                      <p className={`text-2xl font-bold ${getScoreColor(driver.paymentScore)}`}>
                        {driver.paymentScore}%
                      </p>
                      <p className="text-xs text-gray-500">
                        {driver.onTimePayments}/{driver.totalPayments} on time
                      </p>
                    </div>
                    <DollarSign className={`h-8 w-8 ${getScoreColor(driver.paymentScore)}`} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Vehicle Care</p>
                      <p className={`text-2xl font-bold ${getScoreColor(driver.vehicleConditionScore)}`}>
                        {driver.vehicleConditionScore}%
                      </p>
                      <p className="text-xs text-gray-500">
                        Last inspection: {driver.lastInspectionScore}%
                      </p>
                    </div>
                    <Wrench className={`h-8 w-8 ${getScoreColor(driver.vehicleConditionScore)}`} />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Platform Rating</p>
                      <p className="text-2xl font-bold text-blue-600">
                        {driver.platformRating || "N/A"}
                      </p>
                      <p className="text-xs text-gray-500">
                        {driver.totalTrips || 0} trips
                      </p>
                    </div>
                    <Star className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-500">Monthly Earnings</p>
                      <p className="text-2xl font-bold text-[#009639]">
                        R{(driver.monthlyEarnings || 0).toLocaleString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        This month
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-[#009639]" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Issues and Warnings */}
            {(driver.warnings > 0 || driver.activeDisputes > 0 || driver.totalOutstanding > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-red-600">
                    <AlertTriangle size={20} />
                    Active Issues
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {driver.warnings > 0 && (
                      <div className="p-4 border border-yellow-200 rounded-lg bg-yellow-50">
                        <p className="font-medium text-yellow-800">Warnings</p>
                        <p className="text-2xl font-bold text-yellow-600">{driver.warnings}</p>
                      </div>
                    )}
                    {driver.activeDisputes > 0 && (
                      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                        <p className="font-medium text-red-800">Active Disputes</p>
                        <p className="text-2xl font-bold text-red-600">{driver.activeDisputes}</p>
                      </div>
                    )}
                    {driver.totalOutstanding > 0 && (
                      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                        <p className="font-medium text-red-800">Outstanding Balance</p>
                        <p className="text-2xl font-bold text-red-600">
                          R{driver.totalOutstanding.toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            {/* Performance Metrics */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 size={20} className="text-[#009639]" />
                    Payment Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Total Payments</span>
                      <span className="font-bold">{driver.totalPayments}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>On-time Payments</span>
                      <span className="font-bold text-green-600">{driver.onTimePayments}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Late Payments</span>
                      <span className="font-bold text-yellow-600">{driver.latePayments}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Missed Payments</span>
                      <span className="font-bold text-red-600">{driver.missedPayments}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Outstanding Balance</span>
                      <span className={`font-bold ${driver.totalOutstanding > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        R{driver.totalOutstanding.toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wrench size={20} className="text-[#009639]" />
                    Vehicle Care
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Condition Score</span>
                      <Badge variant="outline" className={getScoreBadgeColor(driver.vehicleConditionScore)}>
                        {driver.vehicleConditionScore}%
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Maintenance Requests</span>
                      <span className="font-bold">{driver.maintenanceRequests}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Damage Reports</span>
                      <span className={`font-bold ${driver.damageReports > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {driver.damageReports}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Last Inspection</span>
                      <span className="font-bold">{new Date(driver.lastInspectionDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Inspection Score</span>
                      <Badge variant="outline" className={getScoreBadgeColor(driver.lastInspectionScore)}>
                        {driver.lastInspectionScore}%
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Platform Performance */}
            {driver.platformRating && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star size={20} className="text-[#009639]" />
                    Platform Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <p className="text-sm text-gray-500">Platform Rating</p>
                      <p className="text-3xl font-bold text-blue-600">{driver.platformRating}</p>
                      <div className="flex justify-center mt-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            size={16}
                            className={i < Math.floor(driver.platformRating!) ? "text-yellow-400 fill-current" : "text-gray-300"}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-500">Total Trips</p>
                      <p className="text-3xl font-bold text-[#009639]">{driver.totalTrips}</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-500">Monthly Earnings</p>
                      <p className="text-3xl font-bold text-[#009639]">
                        R{(driver.monthlyEarnings || 0).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="compliance" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Document Compliance */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText size={20} className="text-[#009639]" />
                    Document Compliance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Documents Up to Date</span>
                      <Badge variant="outline" className={driver.documentsUpToDate ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                        {driver.documentsUpToDate ? <CheckCircle size={14} className="mr-1" /> : <AlertTriangle size={14} className="mr-1" />}
                        {driver.documentsUpToDate ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Insurance Current</span>
                      <Badge variant="outline" className={driver.insuranceCurrent ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                        {driver.insuranceCurrent ? <CheckCircle size={14} className="mr-1" /> : <AlertTriangle size={14} className="mr-1" />}
                        {driver.insuranceCurrent ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>License Valid</span>
                      <Badge variant="outline" className={driver.licenseValid ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                        {driver.licenseValid ? <CheckCircle size={14} className="mr-1" /> : <AlertTriangle size={14} className="mr-1" />}
                        {driver.licenseValid ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Disputes and Issues */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle size={20} className="text-[#009639]" />
                    Disputes & Issues
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span>Active Disputes</span>
                      <span className={`font-bold ${driver.activeDisputes > 0 ? 'text-red-600' : 'text-green-600'}`}>
                        {driver.activeDisputes}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Resolved Disputes</span>
                      <span className="font-bold text-gray-600">{driver.resolvedDisputes}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Warnings Issued</span>
                      <span className={`font-bold ${driver.warnings > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                        {driver.warnings}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Payment Score</TableHead>
                        <TableHead>Vehicle Score</TableHead>
                        <TableHead>Platform Rating</TableHead>
                        <TableHead>Weekly Trips</TableHead>
                        <TableHead>Weekly Earnings</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {performanceHistory.map((metric, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            {new Date(metric.date).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className={getScoreBadgeColor(metric.paymentScore)}>
                              {metric.paymentScore}%
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline" className={getScoreBadgeColor(metric.vehicleScore)}>
                              {metric.vehicleScore}%
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              <Star size={14} className="text-yellow-500" />
                              {metric.platformRating.toFixed(1)}
                            </div>
                          </TableCell>
                          <TableCell>{metric.trips}</TableCell>
                          <TableCell>R{metric.earnings.toLocaleString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
