"use client";

import React, { useState, useEffect } from "react";
import { useBodyStyleCleanup } from "../../hooks/useBodyStyleCleanup";
import {
  User,
  Car,
  DollarSign,
  FileText,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Upload,
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  applicationId: string;
  approvalDate: string;
  weeklyRate: number;
  initiationFee: number;
}

interface Vehicle {
  id: string;
  make: string;
  model: string;
  year: number;
  color: string;
  registration: string;
  status: "available" | "assigned" | "maintenance";
  location: string;
}

interface VehicleAssignmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (assignmentData: any) => void;
  approvedDrivers: Driver[];
  availableVehicles: Vehicle[];
}

export default function VehicleAssignmentDialog({
  isOpen,
  onClose,
  onConfirm,
  approvedDrivers,
  availableVehicles,
}: VehicleAssignmentDialogProps) {
  // Use cleanup hook to prevent body style issues
  useBodyStyleCleanup(isOpen);

  const [currentStep, setCurrentStep] = useState<
    "select" | "contract" | "confirm"
  >("select");
  const [selectedDriver, setSelectedDriver] = useState<Driver | null>(null);
  const [selectedVehicle, setSelectedVehicle] = useState<Vehicle | null>(null);
  const [contractFile, setContractFile] = useState<File | null>(null);
  const [initiationFeePaid, setInitiationFeePaid] = useState<number>(0);
  const [paymentArrangement, setPaymentArrangement] = useState<string>("");
  const [assignmentNotes, setAssignmentNotes] = useState<string>("");
  const [assignmentDate, setAssignmentDate] = useState<string>(
    new Date().toISOString().split("T")[0]
  );

  const handleDriverSelect = (driverId: string) => {
    const driver = approvedDrivers.find((d) => d.id === driverId);
    setSelectedDriver(driver || null);
  };

  const handleVehicleSelect = (vehicleId: string) => {
    const vehicle = availableVehicles.find((v) => v.id === vehicleId);
    setSelectedVehicle(vehicle || null);
  };

  const handleContractUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setContractFile(file);
    }
  };

  const handleNext = () => {
    if (currentStep === "select" && selectedDriver && selectedVehicle) {
      setCurrentStep("contract");
    } else if (currentStep === "contract") {
      setCurrentStep("confirm");
    }
  };

  const handleBack = () => {
    if (currentStep === "contract") {
      setCurrentStep("select");
    } else if (currentStep === "confirm") {
      setCurrentStep("contract");
    }
  };

  const handleConfirm = () => {
    if (selectedDriver && selectedVehicle) {
      const assignmentData = {
        driverId: selectedDriver.id,
        vehicleId: selectedVehicle.id,
        assignmentDate,
        contractFile,
        initiationFeePaid,
        paymentArrangement,
        notes: assignmentNotes,
        weeklyRate: selectedDriver.weeklyRate,
        initiationFee: selectedDriver.initiationFee,
      };
      onConfirm(assignmentData);
      // Don't call onClose or resetForm here - let the parent handle it after successful submission
    }
  };

  const resetForm = () => {
    setCurrentStep("select");
    setSelectedDriver(null);
    setSelectedVehicle(null);
    setContractFile(null);
    setInitiationFeePaid(0);
    setPaymentArrangement("");
    setAssignmentNotes("");
    setAssignmentDate(new Date().toISOString().split("T")[0]);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      resetForm();
      onClose();
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "select":
        return "Select Driver & Vehicle";
      case "contract":
        return "Contract & Payment Details";
      case "confirm":
        return "Confirm Assignment";
      default:
        return "";
    }
  };

  const canProceedFromSelect = () => {
    return selectedDriver && selectedVehicle;
  };

  const canProceedFromContract = () => {
    return contractFile && assignmentDate;
  };

  const outstandingBalance = selectedDriver
    ? selectedDriver.initiationFee - initiationFeePaid
    : 0;

  // Add useEffect to reset form when dialog closes
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl">{getStepTitle()}</DialogTitle>
          <DialogDescription>
            {currentStep === "select" &&
              "Choose an approved driver and available vehicle for assignment"}
            {currentStep === "contract" &&
              "Upload contract and set payment details"}
            {currentStep === "confirm" &&
              "Review and confirm the vehicle assignment"}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Step 1: Select Driver & Vehicle */}
          {currentStep === "select" && (
            <div className="space-y-6">
              {/* Driver Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <User className="h-5 w-5 text-[#009639]" />
                    Select Approved Driver
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Select onValueChange={handleDriverSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a driver..." />
                    </SelectTrigger>
                    <SelectContent>
                      {approvedDrivers.map((driver) => (
                        <SelectItem key={driver.id} value={driver.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{driver.name}</span>
                            <span className="text-sm text-gray-500">
                              {driver.email} • R{driver.weeklyRate}/week
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {selectedDriver && (
                    <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{selectedDriver.name}</h4>
                          <p className="text-sm text-gray-600">
                            {selectedDriver.email}
                          </p>
                          <p className="text-sm text-gray-600">
                            {selectedDriver.phone}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">Weekly Rate</p>
                          <p className="font-bold text-[#009639]">
                            R{selectedDriver.weeklyRate.toLocaleString()}
                          </p>
                          <p className="text-xs text-gray-500">
                            Initiation: R
                            {selectedDriver.initiationFee.toLocaleString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Vehicle Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Car className="h-5 w-5 text-[#009639]" />
                    Select Available Vehicle
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Select onValueChange={handleVehicleSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a vehicle..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableVehicles.map((vehicle) => (
                        <SelectItem key={vehicle.id} value={vehicle.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {vehicle.make} {vehicle.model} {vehicle.year}
                            </span>
                            <span className="text-sm text-gray-500">
                              {vehicle.color} • {vehicle.registration} •{" "}
                              {vehicle.location}
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {selectedVehicle && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">
                            {selectedVehicle.make} {selectedVehicle.model}{" "}
                            {selectedVehicle.year}
                          </h4>
                          <p className="text-sm text-gray-600">
                            {selectedVehicle.color} •{" "}
                            {selectedVehicle.registration}
                          </p>
                          <p className="text-sm text-gray-600">
                            Location: {selectedVehicle.location}
                          </p>
                        </div>
                        <Badge
                          variant="outline"
                          className="bg-green-100 text-green-800"
                        >
                          Available
                        </Badge>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Step 2: Contract & Payment */}
          {currentStep === "contract" && selectedDriver && selectedVehicle && (
            <div className="space-y-6">
              {/* Assignment Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Calendar className="h-5 w-5 text-[#009639]" />
                    Assignment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="assignmentDate">Assignment Date</Label>
                    <Input
                      id="assignmentDate"
                      type="date"
                      value={assignmentDate}
                      onChange={(e) => setAssignmentDate(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Contract Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <FileText className="h-5 w-5 text-[#009639]" />
                    Contract Upload
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="contract">Signed Contract</Label>
                      <div className="mt-2">
                        <input
                          id="contract"
                          type="file"
                          accept=".pdf,.doc,.docx"
                          onChange={handleContractUpload}
                          className="hidden"
                        />
                        <label
                          htmlFor="contract"
                          className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors"
                        >
                          <div className="text-center">
                            <Upload
                              size={32}
                              className="mx-auto mb-2 text-gray-400"
                            />
                            <p className="text-sm text-gray-600">
                              {contractFile
                                ? contractFile.name
                                : "Click to upload signed contract"}
                            </p>
                            <p className="text-xs text-gray-500">
                              PDF, DOC, or DOCX files
                            </p>
                          </div>
                        </label>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Details */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <DollarSign className="h-5 w-5 text-[#009639]" />
                    Payment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Total Initiation Fee</Label>
                      <div className="text-lg font-bold text-[#009639]">
                        R{selectedDriver.initiationFee.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="initiationFeePaid">Amount Paid</Label>
                      <Input
                        id="initiationFeePaid"
                        type="number"
                        value={initiationFeePaid}
                        onChange={(e) =>
                          setInitiationFeePaid(Number(e.target.value))
                        }
                        placeholder="0"
                        className="mt-1"
                      />
                    </div>
                  </div>

                  {outstandingBalance > 0 && (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <div className="flex items-center gap-2">
                        <AlertTriangle size={16} className="text-yellow-600" />
                        <span className="font-medium text-yellow-800">
                          Outstanding Balance: R
                          {outstandingBalance.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )}

                  <div>
                    <Label htmlFor="paymentArrangement">
                      Payment Arrangement
                    </Label>
                    <Select onValueChange={setPaymentArrangement}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select payment arrangement..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full_upfront">
                          Full Payment Upfront
                        </SelectItem>
                        <SelectItem value="weekly_deduction">
                          Weekly Deduction from Earnings
                        </SelectItem>
                        <SelectItem value="monthly_installment">
                          Monthly Installments
                        </SelectItem>
                        <SelectItem value="custom">
                          Custom Arrangement
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="notes">Assignment Notes</Label>
                    <Textarea
                      id="notes"
                      value={assignmentNotes}
                      onChange={(e) => setAssignmentNotes(e.target.value)}
                      placeholder="Any special notes or arrangements..."
                      className="mt-1"
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Step 3: Confirmation */}
          {currentStep === "confirm" && selectedDriver && selectedVehicle && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <CheckCircle className="h-5 w-5 text-[#009639]" />
                    Assignment Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">
                        Driver Details
                      </h4>
                      <div className="space-y-1">
                        <p className="font-medium">{selectedDriver.name}</p>
                        <p className="text-sm text-gray-600">
                          {selectedDriver.email}
                        </p>
                        <p className="text-sm text-gray-600">
                          {selectedDriver.phone}
                        </p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">
                        Vehicle Details
                      </h4>
                      <div className="space-y-1">
                        <p className="font-medium">
                          {selectedVehicle.make} {selectedVehicle.model}{" "}
                          {selectedVehicle.year}
                        </p>
                        <p className="text-sm text-gray-600">
                          {selectedVehicle.color} •{" "}
                          {selectedVehicle.registration}
                        </p>
                        <p className="text-sm text-gray-600">
                          Location: {selectedVehicle.location}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-700 mb-2">
                      Financial Summary
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-gray-600">Weekly Rate</p>
                        <p className="font-medium">
                          R{selectedDriver.weeklyRate.toLocaleString()}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">
                          Initiation Fee Paid
                        </p>
                        <p className="font-medium">
                          R{initiationFeePaid.toLocaleString()}
                        </p>
                      </div>
                      {outstandingBalance > 0 && (
                        <div>
                          <p className="text-sm text-gray-600">
                            Outstanding Balance
                          </p>
                          <p className="font-medium text-red-600">
                            R{outstandingBalance.toLocaleString()}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="font-medium text-gray-700 mb-2">
                      Assignment Details
                    </h4>
                    <div className="space-y-2">
                      <p className="text-sm">
                        <span className="text-gray-600">Assignment Date:</span>{" "}
                        {new Date(assignmentDate).toLocaleDateString()}
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-600">Contract:</span>{" "}
                        {contractFile ? contractFile.name : "Not uploaded"}
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-600">
                          Payment Arrangement:
                        </span>{" "}
                        {paymentArrangement || "Not specified"}
                      </p>
                      {assignmentNotes && (
                        <p className="text-sm">
                          <span className="text-gray-600">Notes:</span>{" "}
                          {assignmentNotes}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <DialogFooter className="flex justify-between">
          <div className="flex gap-3">
            {currentStep !== "select" && (
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
            )}
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
          </div>
          <div>
            {currentStep !== "confirm" ? (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === "select" && !canProceedFromSelect()) ||
                  (currentStep === "contract" && !canProceedFromContract())
                }
                className="bg-[#009639] hover:bg-[#007A2F]"
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleConfirm}
                className="bg-[#009639] hover:bg-[#007A2F]"
              >
                <CheckCircle size={16} className="mr-2" />
                Confirm Assignment
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
