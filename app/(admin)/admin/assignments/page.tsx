"use client";

import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  Filter,
  Plus,
  User,
  Car,
  FileText,
  Edit,
  Eye,
  Settings,
  CheckCircle,
  Clock,
  AlertTriangle,
  DollarSign,
  Calendar,
  Upload,
  Download,
  Handshake,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import VehicleAssignmentDialog from "./components/VehicleAssignmentDialog";
import AssignmentDetailsDialog from "./components/AssignmentDetailsDialog";
import VehicleHandoverDialog from "./components/VehicleHandoverDialog";
import PaymentRecordDialog from "../payments/components/PaymentRecordDialog";
import { forceBodyStyleCleanup } from "../hooks/useBodyStyleCleanup";

interface Assignment {
  id: string;
  driverId: string;
  driverName: string;
  driverEmail: string;
  driverPhone: string;
  vehicleId: string;
  vehicleName: string;
  vehicleRegistration: string;
  assignmentDate: string;
  status: "pending_setup" | "contract_uploaded" | "active" | "terminated";
  weeklyRate: number;
  initiationFee: number;
  initiationFeePaid: number;
  outstandingBalance: number;
  contractUploaded: boolean;
  documentsComplete: boolean;
  lastPaymentDate?: string;
  nextPaymentDue: string;
  paymentStatus: "current" | "overdue" | "pending";
  performanceRating?: number;
}

export default function AssignmentsPage() {
  const [activeTab, setActiveTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [isAssignmentDialogOpen, setIsAssignmentDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] =
    useState<Assignment | null>(null);
  const [detailsMode, setDetailsMode] = useState<"view" | "edit">("view");
  const [selectedAssignmentForPayment, setSelectedAssignmentForPayment] =
    useState<string | undefined>(undefined);
  const [isHandoverDialogOpen, setIsHandoverDialogOpen] = useState(false);
  const [selectedAssignmentForHandover, setSelectedAssignmentForHandover] =
    useState<Assignment | null>(null);

  // Mock data for approved drivers - replace with actual API call
  const [approvedDrivers] = useState([
    {
      id: "drv-004",
      name: "Alice Johnson",
      email: "<EMAIL>",
      phone: "+27 85 123 4567",
      applicationId: "app-004",
      approvalDate: "2024-01-28",
      weeklyRate: 2700,
      initiationFee: 7500,
    },
    {
      id: "drv-005",
      name: "Bob Wilson",
      email: "<EMAIL>",
      phone: "+27 86 987 6543",
      applicationId: "app-005",
      approvalDate: "2024-01-29",
      weeklyRate: 2900,
      initiationFee: 8000,
    },
  ]);

  // Mock data for available vehicles - replace with actual API call
  const [availableVehicles] = useState([
    {
      id: "inv-005",
      make: "Suzuki",
      model: "Dzire",
      year: 2023,
      color: "Blue",
      registration: "WC 456-789",
      status: "available" as const,
      location: "Cape Town",
    },
    {
      id: "inv-006",
      make: "Toyota",
      model: "Corolla Quest",
      year: 2022,
      color: "White",
      registration: "GP 123-789",
      status: "available" as const,
      location: "Johannesburg",
    },
  ]);

  // Mock data - replace with actual API call
  const [assignments] = useState<Assignment[]>([
    {
      id: "assign-001",
      driverId: "drv-001",
      driverName: "John Doe",
      driverEmail: "<EMAIL>",
      driverPhone: "+27 82 123 4567",
      vehicleId: "inv-001",
      vehicleName: "Suzuki Dzire 2023",
      vehicleRegistration: "CA 123-456",
      assignmentDate: "2024-01-15",
      status: "active",
      weeklyRate: 2700,
      initiationFee: 7500,
      initiationFeePaid: 7500,
      outstandingBalance: 0,
      contractUploaded: true,
      documentsComplete: true,
      lastPaymentDate: "2024-01-22",
      nextPaymentDue: "2024-01-29",
      paymentStatus: "current",
      performanceRating: 4.8,
    },
    {
      id: "assign-002",
      driverId: "drv-002",
      driverName: "Sarah Smith",
      driverEmail: "<EMAIL>",
      driverPhone: "+27 83 987 6543",
      vehicleId: "inv-003",
      vehicleName: "Toyota Corolla Quest 2022",
      vehicleRegistration: "GP 345-678",
      assignmentDate: "2024-01-20",
      status: "contract_uploaded",
      weeklyRate: 2900,
      initiationFee: 8000,
      initiationFeePaid: 5000,
      outstandingBalance: 3000,
      contractUploaded: true,
      documentsComplete: false,
      nextPaymentDue: "2024-02-05",
      paymentStatus: "pending",
    },
    {
      id: "assign-003",
      driverId: "drv-003",
      driverName: "Mike Johnson",
      driverEmail: "<EMAIL>",
      driverPhone: "+27 84 555 1234",
      vehicleId: "inv-004",
      vehicleName: "Nissan Almera 2023",
      vehicleRegistration: "KZN 789-012",
      assignmentDate: "2024-01-25",
      status: "pending_setup",
      weeklyRate: 2800,
      initiationFee: 7800,
      initiationFeePaid: 0,
      outstandingBalance: 7800,
      contractUploaded: false,
      documentsComplete: true,
      nextPaymentDue: "2024-02-01",
      paymentStatus: "pending",
    },
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "contract_uploaded":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "pending_setup":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "terminated":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle size={12} className="mr-1" />;
      case "contract_uploaded":
        return <FileText size={12} className="mr-1" />;
      case "pending_setup":
        return <Clock size={12} className="mr-1" />;
      case "terminated":
        return <AlertTriangle size={12} className="mr-1" />;
      default:
        return <Clock size={12} className="mr-1" />;
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case "current":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const filteredAssignments = assignments.filter((assignment) => {
    const matchesSearch =
      assignment.driverName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assignment.vehicleName
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      assignment.vehicleRegistration
        .toLowerCase()
        .includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === "all" || assignment.status === filterStatus;
    const matchesTab =
      activeTab === "all" ||
      (activeTab === "active" && assignment.status === "active") ||
      (activeTab === "pending" &&
        (assignment.status === "pending_setup" ||
          assignment.status === "contract_uploaded")) ||
      (activeTab === "overdue" && assignment.paymentStatus === "overdue");

    return matchesSearch && matchesStatus && matchesTab;
  });

  const getTabCount = (tab: string) => {
    switch (tab) {
      case "all":
        return assignments.length;
      case "active":
        return assignments.filter((a) => a.status === "active").length;
      case "pending":
        return assignments.filter(
          (a) =>
            a.status === "pending_setup" || a.status === "contract_uploaded"
        ).length;
      case "overdue":
        return assignments.filter((a) => a.paymentStatus === "overdue").length;
      default:
        return 0;
    }
  };

  const handleNewAssignment = () => {
    setIsAssignmentDialogOpen(true);
  };

  const handleAssignmentSubmit = (assignmentData: any) => {
    // TODO: Implement API call to create assignment
    console.log("Assignment data:", assignmentData);

    // Only close dialog after successful submission
    // In a real app, this would be inside a try/catch with API call
    setTimeout(() => {
      handleCloseAssignmentDialog();
      // You could also show a success toast here
    }, 100);
  };

  const handleCloseAssignmentDialog = () => {
    setIsAssignmentDialogOpen(false);
    // Force cleanup of body styles to prevent freezing
    setTimeout(() => {
      forceBodyStyleCleanup();
    }, 100);
  };

  const handleViewAssignment = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignment(assignment);
      setDetailsMode("view");
      setIsDetailsDialogOpen(true);
    }, 100);
  };

  const handleEditAssignment = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignment(assignment);
      setDetailsMode("edit");
      setIsDetailsDialogOpen(true);
    }, 100);
  };

  const handleCloseDetailsDialog = () => {
    setIsDetailsDialogOpen(false);
    // Don't reset selectedAssignment immediately to avoid state conflicts
    setTimeout(() => {
      setSelectedAssignment(null);
      // Force cleanup of body styles to prevent freezing
      forceBodyStyleCleanup();
    }, 150);
  };

  const handleUpdateAssignment = (assignmentData: any) => {
    // TODO: Implement API call to update assignment
    console.log("Update assignment:", assignmentData);

    // Only close dialog after successful update
    setTimeout(() => {
      handleCloseDetailsDialog();
      // You could also show a success toast here
    }, 100);
  };

  const handleRecordPaymentFromDetails = (assignmentId: string) => {
    // Close details dialog and open payment dialog
    handleCloseDetailsDialog();
    setSelectedAssignmentForPayment(assignmentId);
    setIsPaymentDialogOpen(true);
  };

  const handleRecordPaymentFromTable = (assignmentId: string) => {
    setSelectedAssignmentForPayment(assignmentId);
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = (paymentData: any) => {
    // TODO: Implement API call to record payment
    console.log("Payment data:", paymentData);
    setIsPaymentDialogOpen(false);
    setSelectedAssignmentForPayment(undefined);
  };

  const handleClosePaymentDialog = () => {
    setIsPaymentDialogOpen(false);
    setSelectedAssignmentForPayment(undefined);
  };

  const handleTerminateAssignment = (assignmentId: string, reason: string) => {
    // TODO: Implement API call to terminate assignment
    console.log("Terminate assignment:", assignmentId, "Reason:", reason);
    handleCloseDetailsDialog();
  };

  const handleInitiateHandover = (assignment: Assignment) => {
    // Small delay to ensure dropdown closes before modal opens
    setTimeout(() => {
      setSelectedAssignmentForHandover(assignment);
      setIsHandoverDialogOpen(true);
    }, 100);
  };

  const handleHandoverSubmit = (handoverData: any) => {
    // TODO: Implement API call to initiate handover
    console.log("Handover data:", handoverData);
    handleCloseHandoverDialog();
  };

  const handleCloseHandoverDialog = () => {
    setIsHandoverDialogOpen(false);
    setSelectedAssignmentForHandover(null);
    // Force cleanup of body styles to prevent freezing
    setTimeout(() => {
      forceBodyStyleCleanup();
    }, 100);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">
            Vehicle Assignments
          </h1>
          <p className="text-gray-600 mt-1">
            Manage post-approval driver assignments and contracts
          </p>
        </div>
        <Button
          onClick={handleNewAssignment}
          className="bg-[#009639] hover:bg-[#007A2F] text-white flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Assignment
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Assignments</p>
                <p className="text-2xl font-bold mt-1">{assignments.length}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                <User className="text-blue-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Active</p>
                <p className="text-2xl font-bold mt-1">
                  {assignments.filter((a) => a.status === "active").length}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="text-green-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Pending Setup</p>
                <p className="text-2xl font-bold mt-1">
                  {
                    assignments.filter(
                      (a) =>
                        a.status === "pending_setup" ||
                        a.status === "contract_uploaded"
                    ).length
                  }
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="text-yellow-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Outstanding Balance</p>
                <p className="text-2xl font-bold mt-1">
                  R
                  {assignments
                    .reduce((sum, a) => sum + a.outstandingBalance, 0)
                    .toLocaleString()}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                <DollarSign className="text-red-600" size={20} />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Tabs */}
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All Assignments ({getTabCount("all")})
                </TabsTrigger>
                <TabsTrigger value="active" className="flex items-center gap-2">
                  <CheckCircle size={16} />
                  Active ({getTabCount("active")})
                </TabsTrigger>
                <TabsTrigger
                  value="pending"
                  className="flex items-center gap-2"
                >
                  <Clock size={16} />
                  Pending Setup ({getTabCount("pending")})
                </TabsTrigger>
                <TabsTrigger
                  value="overdue"
                  className="flex items-center gap-2"
                >
                  <AlertTriangle size={16} />
                  Overdue ({getTabCount("overdue")})
                </TabsTrigger>
              </TabsList>
            </Tabs>

            {/* Search and Filters */}
            <div className="flex gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search assignments..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-[250px]"
                />
              </div>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md text-sm"
                aria-label="Filter by assignment status"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="contract_uploaded">Contract Uploaded</option>
                <option value="pending_setup">Pending Setup</option>
                <option value="terminated">Terminated</option>
              </select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Driver</TableHead>
                  <TableHead>Vehicle</TableHead>
                  <TableHead>Assignment Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Financial Status</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssignments.map((assignment) => (
                  <TableRow key={assignment.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {assignment.driverName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {assignment.driverEmail}
                        </div>
                        <div className="text-xs text-gray-400">
                          {assignment.driverPhone}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {assignment.vehicleName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {assignment.vehicleRegistration}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center text-sm">
                        <Calendar size={14} className="mr-1 text-gray-400" />
                        {new Date(
                          assignment.assignmentDate
                        ).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(assignment.status)}
                      >
                        {getStatusIcon(assignment.status)}
                        {assignment.status
                          .replace("_", " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="text-sm">
                          <span className="font-medium">
                            R{assignment.weeklyRate.toLocaleString()}
                          </span>
                          <span className="text-gray-500">/week</span>
                        </div>
                        {assignment.outstandingBalance > 0 && (
                          <div className="text-xs text-red-600">
                            Outstanding: R
                            {assignment.outstandingBalance.toLocaleString()}
                          </div>
                        )}
                        <div className="flex items-center gap-2 mt-1">
                          {assignment.contractUploaded ? (
                            <CheckCircle size={12} className="text-green-500" />
                          ) : (
                            <AlertTriangle
                              size={12}
                              className="text-yellow-500"
                            />
                          )}
                          <span className="text-xs text-gray-500">
                            {assignment.contractUploaded
                              ? "Contract"
                              : "No Contract"}
                          </span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getPaymentStatusColor(
                          assignment.paymentStatus
                        )}
                      >
                        {assignment.paymentStatus.charAt(0).toUpperCase() +
                          assignment.paymentStatus.slice(1)}
                      </Badge>
                      {assignment.nextPaymentDue && (
                        <div className="text-xs text-gray-500 mt-1">
                          Due:{" "}
                          {new Date(
                            assignment.nextPaymentDue
                          ).toLocaleDateString()}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <Settings size={16} />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.preventDefault();
                              handleViewAssignment(assignment);
                            }}
                          >
                            <Eye size={14} className="mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.preventDefault();
                              handleEditAssignment(assignment);
                            }}
                          >
                            <Edit size={14} className="mr-2" />
                            Edit Assignment
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {!assignment.contractUploaded && (
                            <DropdownMenuItem className="text-blue-600">
                              <Upload size={14} className="mr-2" />
                              Upload Contract
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem
                            onClick={() =>
                              handleRecordPaymentFromTable(assignment.id)
                            }
                          >
                            <DollarSign size={14} className="mr-2" />
                            Record Payment
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download size={14} className="mr-2" />
                            Generate Report
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {assignment.status === "active" && (
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.preventDefault();
                                handleInitiateHandover(assignment);
                              }}
                              className="text-[#009639]"
                            >
                              <Handshake size={14} className="mr-2" />
                              Initiate Handover
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuItem className="text-red-600">
                            <AlertTriangle size={14} className="mr-2" />
                            Terminate Assignment
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Assignment Dialog */}
      <VehicleAssignmentDialog
        isOpen={isAssignmentDialogOpen}
        onClose={handleCloseAssignmentDialog}
        onConfirm={handleAssignmentSubmit}
        approvedDrivers={approvedDrivers}
        availableVehicles={availableVehicles}
      />

      {/* Assignment Details Dialog */}
      <AssignmentDetailsDialog
        isOpen={isDetailsDialogOpen}
        onClose={handleCloseDetailsDialog}
        assignment={selectedAssignment}
        mode={detailsMode}
        onUpdate={handleUpdateAssignment}
        onRecordPayment={handleRecordPaymentFromDetails}
        onTerminate={handleTerminateAssignment}
      />

      {/* Payment Record Dialog */}
      <PaymentRecordDialog
        isOpen={isPaymentDialogOpen}
        onClose={handleClosePaymentDialog}
        onConfirm={handlePaymentSubmit}
        assignments={assignments.map((assignment) => ({
          id: assignment.id,
          driverName: assignment.driverName,
          vehicleName: assignment.vehicleName,
          vehicleRegistration: assignment.vehicleRegistration,
          weeklyRate: assignment.weeklyRate,
          outstandingBalance: assignment.outstandingBalance,
        }))}
        selectedAssignmentId={selectedAssignmentForPayment}
      />

      {/* Vehicle Handover Dialog */}
      <VehicleHandoverDialog
        isOpen={isHandoverDialogOpen}
        onClose={handleCloseHandoverDialog}
        onConfirm={handleHandoverSubmit}
        assignment={selectedAssignmentForHandover}
      />
    </div>
  );
}
