import type { PostConfirma<PERSON>T<PERSON>gerHand<PERSON> } from "aws-lambda";
import { Pool } from "pg";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import {
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
  AdminAddUserToGroupCommand,
} from "@aws-sdk/client-cognito-identity-provider";

async function updateUserAttributes(
  username: string,
  attributes: Record<string, string>
) {
  const userPoolId = process.env.COGNITO_USER_POOL_ID || "eu-west-1_UJEZ4brMP";
  if (!userPoolId) throw new Error("COGNITO_USER_POOL_ID is not set");

  const userAttributes = Object.entries(attributes).map(([Name, Value]) => ({
    Name,
    Value: String(Value),
  }));

  const command = new AdminUpdateUserAttributesCommand({
    UserPoolId: userPoolId,
    Username: username,
    UserAttributes: userAttributes,
  });

  try {
    const cognito = new CognitoIdentityProviderClient({
      region: process.env.AWS_REGION || "eu-west-1",
    });

    await cognito.send(command);
    console.log(`Successfully updated attributes for user: ${username}`);
  } catch (err) {
    console.error("Failed to update user attributes:", err);
    throw err;
  }
}

async function addUserToGroup(username: string, groupName: string) {
  const userPoolId = process.env.COGNITO_USER_POOL_ID || "eu-west-1_UJEZ4brMP";
  if (!userPoolId) throw new Error("COGNITO_USER_POOL_ID is not set");

  const command = new AdminAddUserToGroupCommand({
    UserPoolId: userPoolId,
    Username: username,
    GroupName: groupName,
  });

  try {
    const cognito = new CognitoIdentityProviderClient({
      region: process.env.AWS_REGION || "eu-west-1",
    });

    await cognito.send(command);
    console.log(`User ${username} added to group ${groupName}`);
  } catch (err) {
    console.error("Failed to add user to group:", err);
    throw err;
  }
}

const secretsManager = new SecretsManagerClient({
  region: process.env.AWS_REGION || "us-east-1",
});

async function getDatabaseCredentials(secretId: string) {
  try {
    const command = new GetSecretValueCommand({ SecretId: secretId });
    const response = await secretsManager.send(command);

    if (!response.SecretString) {
      throw new Error("Secret string is empty");
    }
    return JSON.parse(response.SecretString);
  } catch (error) {
    console.error("Error retrieving secret:", error);
    throw new Error("Failed to retrieve database credentials");
  }
}

// Configure the PostgreSQL connection pool
async function createPool() {
  const secret = await getDatabaseCredentials(
    process.env.DB_SECRET_ID || "poolly-database-secret"
  );

  return new Pool({
    host: secret.POSTGRES_HOST,
    user: secret.POSTGRES_USER,
    password: secret.POSTGRES_PASSWORD,
    database: secret.POSTGRES_DB,
    port: secret.port || 5432,
    ssl: { rejectUnauthorized: false },
  });
}

export const handler: PostConfirmationTriggerHandler = async (event) => {
  let pool;
  let client;

  try {
    // Initialize the connection pool
    pool = await createPool();
    pool.on("error", (err) => {
      console.error("Unexpected error on idle client", err);
    });
    client = await pool.connect();
    console.log("Connected!");
    const res = await client.query("SELECT 1");
    console.log("Query result:", res.rows);
    console.log("Database connection verified.");

    // SQL query from the provided script
    const query = `
        WITH email_contact_type AS (
          INSERT INTO public.contact_point_type (name, description, validation_pattern, is_active, created_at, updated_at)
          VALUES ('email', 'Email', 'string', TRUE, NOW(), NOW())
          ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
          RETURNING id
        ),
        active_status AS (
          INSERT INTO public.party_status (name, description, is_active, created_at, updated_at)
          VALUES ('Active', 'Active Party', TRUE, NOW(), NOW())
          ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
          RETURNING id
        ),
        individual_party_type AS (
          INSERT INTO public.party_type (name, description, is_active, created_at, updated_at)
          VALUES ('Individual', 'Individual party type', TRUE, NOW(), NOW())
          ON CONFLICT (name) DO UPDATE SET updated_at = NOW()
          RETURNING id
        ),
        new_party AS (
          INSERT INTO public.party (party_type_id, status_id, external_id, created_at, updated_at)
          SELECT ipt.id, ps.id, $1, NOW(), NOW()
          FROM individual_party_type ipt, active_status ps
          RETURNING id, external_id
        ),
        new_individual AS (
          INSERT INTO public.individual (party_id, first_name, last_name, created_at, updated_at)
          SELECT id, $2, $3, NOW(), NOW()
          FROM new_party
          RETURNING party_id
        ),
        new_contact AS (
          INSERT INTO public.contact_point (party_id, contact_point_type_id, value, is_primary, is_verified, created_at, updated_at)
          SELECT np.id, ect.id, $4, TRUE, TRUE, NOW(), NOW()
          FROM new_party np, email_contact_type ect
        )
        SELECT 
          p.id AS party_id,
          p.external_id AS sub
        FROM new_party p;
    `;

    // Extract user attributes
    const { email, sub, given_name, family_name } =
      event.request.userAttributes;

    // Validate required attributes
    if (!email || !sub) {
      throw new Error("Missing required attributes: email or sub");
    }

    // Use default values for first_name and last_name if not provided
    const firstName = given_name || "";
    const lastName = family_name || "";

    // Execute the query
    const values = [sub, firstName, lastName, email];
    const result = await client.query(query, values);
    const partyId = result.rows[0]?.party_id;

    await updateUserAttributes(sub, {
      "custom:db_id": String(partyId),
    });
    await addUserToGroup(sub, "GROUP_USER");

    // Return the event to complete the Lambda execution
    return event;
  } catch (error) {
    console.error("Error inserting user profile:", error);
    throw new Error("Failed to create user profile");
  } finally {
    // Release the client and end the pool
    if (client) client.release();
    if (pool) await pool.end();
  }
};
