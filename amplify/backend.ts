// import * as sqs from "aws-cdk-lib/aws-sqs";
import { defineBackend } from "@aws-amplify/backend";
import { auth } from "./auth/resource.js";
import { data } from "./data/resource.js";
import { storage } from "./storage/resource";
import { Effect, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { postConfirmation } from "./auth/post-confirmation/resource.js";
import { CfnFunction } from "aws-cdk-lib/aws-lambda";

const backend = defineBackend({
  auth,
  data,
  storage,
  postConfirmation,
});

const customResourceStack = backend.createStack("PoollyCustomResources");
// const dlq = new sqs.Queue(customResourceStack, `EmailSendDeadLetter`);

// const sourceQueue = new sqs.Queue(customResourceStack, "emailSend", {
//   deadLetterQueue: {
//     queue: dlq,
//     maxReceiveCount: 5,
//   },
// });

// sourceQueue.grantSendMessages(backend.postConfirmation.resources.lambda);

const postConfirmationLambda = backend.postConfirmation.resources.lambda;
// const lambda = backend.postConfirmation.resources.lambda.node
//   .defaultChild as CfnFunction;
// lambda.addPropertyOverride(
//   "Environment.Variables.QUEUE_URL",
//   sourceQueue.queueUrl
// );

postConfirmationLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ["secretsmanager:GetSecretValue"],
    resources: [
      "arn:aws:secretsmanager:eu-west-1:097332785067:secret:poolly-database-secret-*",
    ],
  })
);
postConfirmationLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ["cognito-idp:AdminUpdateUserAttributes"],
    resources: ["arn:aws:cognito-idp:eu-west-1:097332785067:userpool/*"],
  })
);
postConfirmationLambda.addToRolePolicy(
  new PolicyStatement({
    effect: Effect.ALLOW,
    actions: ["cognito-idp:AdminAddUserToGroup"],
    resources: ["arn:aws:cognito-idp:eu-west-1:097332785067:userpool/*"],
  })
);
