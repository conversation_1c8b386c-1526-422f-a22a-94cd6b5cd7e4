"use server";

import { db } from "../db";
import { eq, inArray, and } from "drizzle-orm";
import {
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  companyOwnership,
  company,
  individual,
} from "../drizzle/schema";
import type { VehicleReadWithModelAndParty } from "@/types/vehicles";
import type { CompanyOwnershipReadWithRelations, CompanyMembershipRead } from "@/types/company-ownerships";

export async function getVehiclesByParties(
  partyIds: number[]
): Promise<VehicleReadWithModelAndParty[]> {
  if (partyIds.length === 0) {
    return [];
  }

  try {
    const vehicleResults = await db
      .select({
        // Vehicle fields
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryOfRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        
        // Model fields
        modelName: vehicleModel.model,
        yearModel: vehicleModel.yearModel,
        transmission: vehicleModel.transmission,
        modelDescription: vehicleModel.description,
        
        // Make fields
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        makeDescription: vehicleMake.description,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(inArray(vehicles.partyId, partyIds))
      .orderBy(vehicles.id);

    return vehicleResults.map(vehicle => ({
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_of_registration: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      purchase_date: vehicle.purchaseDate,
      color: vehicle.color,
      is_active: vehicle.isActive,
      created_at: vehicle.createdAt?.toString(),
      updated_at: vehicle.updatedAt?.toString(),
      
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: vehicle.yearModel,
        description: (vehicle.modelDescription || undefined) as string | undefined,
        transmission: vehicle.transmission,
        fuel: undefined,
        is_active: true,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName,
          description: (vehicle.makeDescription || undefined) as string | undefined,
          logo_url: undefined,
          is_active: true,
        }
      },
      
      // These arrays would need additional queries in a real implementation
      // For now, we'll return empty arrays to match the expected type
      bookings: [],
      maintenance_items: [],
      inspections: [],
      media: [],
      vehicle_documents: [],
    })) as VehicleReadWithModelAndParty[];
    
  } catch (error) {
    console.error("Error fetching vehicles by parties:", error);
    return [];
  }
}

export async function getCompanyOwnershipByParty(
  partyId: number
): Promise<CompanyOwnershipReadWithRelations[]> {
  try {
    const ownershipResults = await db
      .select({
        // CompanyOwnership fields
        id: companyOwnership.id,
        partyId: companyOwnership.partyId,
        companyId: companyOwnership.companyId,
        fraction: companyOwnership.fraction,
        effectiveFrom: companyOwnership.effectiveFrom,
        effectiveTo: companyOwnership.effectiveTo,
        isActive: companyOwnership.isActive,
        createdAt: companyOwnership.createdAt,
        updatedAt: companyOwnership.updatedAt,
        
        // Company fields
        companyAlias: company.alias,
        companyDescription: company.description,
        companyRegistrationNumber: company.registrationNumber,
        companyRegistrationCountry: company.registrationCountry,
        companyRegistrationDate: company.registrationDate,
        companyPurpose: company.purpose,
        companyPartyId: company.partyId,
        companyCreatedAt: company.createdAt,
        companyUpdatedAt: company.updatedAt,
      })
      .from(companyOwnership)
      .leftJoin(company, eq(companyOwnership.companyId, company.id))
      .where(
        and(
          eq(companyOwnership.partyId, partyId),
          eq(companyOwnership.isActive, true)
        )
      )
      .orderBy(companyOwnership.id);

    return ownershipResults.map(ownership => ({
      id: ownership.id,
      party_id: ownership.partyId,
      company_id: ownership.companyId,
      fraction: parseFloat(ownership.fraction?.toString() || "0"),
      effective_from: ownership.effectiveFrom ? new Date(ownership.effectiveFrom) : new Date(),
      effective_to: ownership.effectiveTo ? new Date(ownership.effectiveTo) : null,
      is_active: ownership.isActive,
      created_at: ownership.createdAt ? new Date(ownership.createdAt) : new Date(),
      updated_at: ownership.updatedAt ? new Date(ownership.updatedAt) : new Date(),
      
      party: null,
      
      company: ownership.companyId ? {
        id: ownership.companyId,
        alias: ownership.companyAlias,
        description: ownership.companyDescription,
        registration_number: ownership.companyRegistrationNumber,
        registration_country: ownership.companyRegistrationCountry,
        registration_date: ownership.companyRegistrationDate?.toString() || "",
        purpose: ownership.companyPurpose,
        party_id: ownership.companyPartyId || 0, // Use 0 as fallback for required field
        created_at: ownership.companyCreatedAt?.toString() || "",
        updated_at: ownership.companyUpdatedAt?.toString() || "",
      } : null,
    })) as CompanyOwnershipReadWithRelations[];
    
  } catch (error) {
    console.error("Error fetching company ownership by party:", error);
    return [];
  }
}

export async function getCompanyOwnershipsByCompanies(
  companyIds: number[]
): Promise<CompanyMembershipRead> {
  if (companyIds.length === 0) {
    return { individuals: [], companies: [] };
  }

  try {
    // Get individuals who are members of these companies
    const individualResults = await db
      .select({
        // Individual fields
        individualId: individual.id,
        firstName: individual.firstName,
        lastName: individual.lastName,
        birthDate: individual.birthDate,
        partyId: individual.partyId,
        
        // CompanyOwnership fields
        fraction: companyOwnership.fraction,
        companyId: companyOwnership.companyId,
      })
      .from(companyOwnership)
      .innerJoin(individual, eq(companyOwnership.partyId, individual.partyId))
      .where(
        and(
          inArray(companyOwnership.companyId, companyIds),
          eq(companyOwnership.isActive, true)
        )
      )
      .orderBy(companyOwnership.companyId, companyOwnership.fraction);

    // Get companies
    const companyResults = await db
      .select({
        id: company.id,
        alias: company.alias,
        description: company.description,
        registrationNumber: company.registrationNumber,
        registrationCountry: company.registrationCountry,
        registrationDate: company.registrationDate,
        purpose: company.purpose,
        partyId: company.partyId,
        createdAt: company.createdAt,
        updatedAt: company.updatedAt,
      })
      .from(company)
      .where(inArray(company.id, companyIds))
      .orderBy(company.id);

    return {
      individuals: individualResults.map(result => ({
        individual: {
          id: result.individualId,
          first_name: result.firstName,
          last_name: result.lastName,
          date_of_birth: result.birthDate,
          party_id: result.partyId,
          username: "", // Required by type but not in schema
          email: "", // Required by type but not in schema
          birth_date: result.birthDate,
          created_at: "",
          updated_at: "",
        } as any,
        fraction: parseFloat(result.fraction?.toString() || "0"),
        company_id: result.companyId,
      })),
      companies: companyResults.map(comp => ({
        id: comp.id,
        alias: comp.alias || "",
        description: comp.description,
        registration_number: comp.registrationNumber,
        registration_country: comp.registrationCountry,
        registration_date: comp.registrationDate?.toString() || "",
        purpose: comp.purpose,
        party_id: comp.partyId || 0,
        created_at: comp.createdAt?.toString() || "",
        updated_at: comp.updatedAt?.toString() || "",
      } as any)),
    } as CompanyMembershipRead;
    
  } catch (error) {
    console.error("Error fetching company ownerships by companies:", error);
    return { individuals: [], companies: [] };
  }
} 