"use server";

/**
 * DRIZZLE ACTIONS - LISTINGS & VEHICLES
 * 
 * This file contains all direct database operations for listings and vehicles using Drizzle ORM.
 * 
 * Architecture:
 * - drizzle-actions/* = Direct database queries using Drizzle ORM
 * - actions/* = Legacy API calls using axios (maintained for backward compatibility)
 * 
 * Schema Tables Used:
 * - listings: Vehicle listings for co-ownership
 * - vehicles: Vehicle records
 * - vehicleModel: Vehicle model information
 * - vehicleMake: Vehicle manufacturer information
 * - party: Vehicle owners
 * - individual: Owner details
 */

import { db } from "../db";
import { eq, and, desc, asc, gte, lte, between, sql, inArray } from "drizzle-orm";
import {
  listings,
  vehicles,
  vehicleModel,
  vehicleMake,
  party,
  individual,
  listingMedia,
  vehicleMedia,
  listingInterestExpressions,
  company,
  companyOwnership,
} from "../drizzle/schema";
import type {
  ListingCreate,
  ListingTypeEnum,
  AudienceEnum,
  ConditionEnum
} from "@/types/listings";
import { getUserAttributes } from "@/lib/serverUserAttributes";

// ==================== TYPES ====================

export interface VehicleCreate {
  partyId: number;
  modelId: number;
  vinNumber: string;
  vehicleRegistration?: string;
  countryOfRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  color?: string;
}

export interface VehicleRead {
  id: number;
  partyId: number;
  modelId: number;
  vinNumber: string;
  vehicleRegistration?: string;
  countryOfRegistration?: string;
  manufacturingYear?: number;
  purchaseDate?: string;
  color?: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
  // Related data
  model?: {
    id: number;
    model: string;
    yearModel: number;
    transmission?: string;
    make?: {
      id: number;
      name: string;
    };
  };
  owner?: {
    firstName: string;
    lastName: string;
  };
}

export interface ListingMediaRead {
  id: number;
  listingId: number;
  mediaPath: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ListingRead {
  id: number;
  partyId: number;
  vehicleId: number;
  effectiveFrom: string;
  effectiveTo: string;
  fractionOffer: number;
  askingPrice: number;
  condition: "new" | "used";
  mileage?: number;
  listingType: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  audience: "BUSINESS" | "E_HAILING" | "CONSUMER";
  createdAt?: string;
  updatedAt?: string;
  // Related data
  vehicle?: VehicleRead;
  owner?: {
    firstName: string;
    lastName: string;
  };
  media?: ListingMediaRead[];
  // Interest expressions
  interestCount?: number;
  userHasExpressedInterest?: boolean;
}

export interface ListingFormData {
  // Vehicle details
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  location: string;
  description: string;
  vinNumber: string;
  vehicleRegistration?: string;
  
  // Listing details
  fractionSize: string;
  fractions: number;
  pricePerFraction: string;
  listingType: string;
  audience: string;
  effectiveFrom: string;
  effectiveTo: string;
  
  // Images
  vehicleImages?: string[];
  
  // New field for fraction offer
  fractionOffer: string;
}

export interface CreateListingRequest {
  vehicle_id: number;
  listing_type: ListingTypeEnum;
  audience: AudienceEnum;
  condition: ConditionEnum;
  asking_price: number;
  fraction_offer: number;
  effective_from: string;
  effective_to: string;
  mileage?: number;
  company_id?: number; // For private group sharing
}

// ==================== VEHICLE CRUD OPERATIONS ====================

// Create a new vehicle
export async function createVehicleDrizzle(
  vehicleData: VehicleCreate
): Promise<VehicleRead> {
  try {
    const newVehicle = await db
      .insert(vehicles)
      .values({
        partyId: vehicleData.partyId,
        modelId: vehicleData.modelId,
        vinNumber: vehicleData.vinNumber,
        vehicleRegistration: vehicleData.vehicleRegistration,
        countryOfRegistration: vehicleData.countryOfRegistration,
        manufacturingYear: vehicleData.manufacturingYear,
        purchaseDate: vehicleData.purchaseDate,
        color: vehicleData.color,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      .returning();

    return {
      id: newVehicle[0].id,
      partyId: newVehicle[0].partyId,
      modelId: newVehicle[0].modelId,
      vinNumber: newVehicle[0].vinNumber,
      vehicleRegistration: newVehicle[0].vehicleRegistration || undefined,
      countryOfRegistration: newVehicle[0].countryOfRegistration || undefined,
      manufacturingYear: newVehicle[0].manufacturingYear || undefined,
      purchaseDate: newVehicle[0].purchaseDate || undefined,
      color: newVehicle[0].color || undefined,
      isActive: newVehicle[0].isActive,
      createdAt: newVehicle[0].createdAt || undefined,
      updatedAt: newVehicle[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating vehicle:", error);
    throw error;
  }
}

// Get vehicle by ID with related data
export async function getVehicleByIdDrizzle(
  vehicleId: number
): Promise<VehicleRead | null> {
  try {
    const vehicle = await db
      .select({
        id: vehicles.id,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vinNumber: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryOfRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        purchaseDate: vehicles.purchaseDate,
        color: vehicles.color,
        isActive: vehicles.isActive,
        createdAt: vehicles.createdAt,
        updatedAt: vehicles.updatedAt,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        transmission: vehicleModel.transmission,
        // Make details
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(vehicles)
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(vehicles.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(vehicles.id, vehicleId))
      .limit(1);

    if (vehicle.length === 0) return null;

    const record = vehicle[0];
    
    return {
      id: record.id,
      partyId: record.partyId,
      modelId: record.modelId,
      vinNumber: record.vinNumber,
      vehicleRegistration: record.vehicleRegistration || undefined,
      countryOfRegistration: record.countryOfRegistration || undefined,
      manufacturingYear: record.manufacturingYear || undefined,
      purchaseDate: record.purchaseDate || undefined,
      color: record.color || undefined,
      isActive: record.isActive,
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      model: record.modelName ? {
        id: record.modelId,
        model: record.modelName,
        yearModel: record.modelYear || 0,
        transmission: record.transmission || undefined,
        make: record.makeName ? {
          id: record.makeId || 0,
          name: record.makeName,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
    };
  } catch (error) {
    console.error("Error fetching vehicle:", error);
    throw error;
  }
}

// ==================== LISTING CRUD OPERATIONS ====================

// Update listing
export async function updateListingDrizzle(
  listingId: number,
  updateData: Partial<ListingCreate>
): Promise<ListingRead> {
  try {
    // Map snake_case fields from ListingCreate to camelCase fields for Drizzle
    const drizzleUpdateData: any = {};
    
    if (updateData.asking_price !== undefined) {
      drizzleUpdateData.askingPrice = updateData.asking_price;
    }
    if (updateData.fraction_offer !== undefined) {
      drizzleUpdateData.fractionOffer = updateData.fraction_offer;
    }
    if (updateData.effective_from !== undefined) {
      drizzleUpdateData.effectiveFrom = updateData.effective_from;
    }
    if (updateData.effective_to !== undefined) {
      drizzleUpdateData.effectiveTo = updateData.effective_to;
    }
    if (updateData.condition !== undefined) {
      drizzleUpdateData.condition = updateData.condition;
    }
    if (updateData.mileage !== undefined) {
      drizzleUpdateData.mileage = updateData.mileage;
    }
    if (updateData.audience !== undefined) {
      drizzleUpdateData.audience = updateData.audience;
    }
    if (updateData.listing_type !== undefined) {
      drizzleUpdateData.listingType = updateData.listing_type;
    }

    console.log("Updating listing with data:", drizzleUpdateData);

    const updatedListing = await db
      .update(listings)
      .set({
        ...drizzleUpdateData,
        updatedAt: new Date().toISOString(),
      })
      .where(eq(listings.id, listingId))
      .returning();

    console.log("Updated listing result:", updatedListing);

    if (updatedListing.length === 0) {
      throw new Error("Listing not found");
    }

    // Get the full listing data with related information
    const fullListingData = await getListingByIdDrizzle(listingId);
    if (!fullListingData) {
      throw new Error("Failed to retrieve updated listing");
    }

    return fullListingData;
  } catch (error) {
    console.error("Error updating listing:", error);
    throw error;
  }
}

// End listing (set effective_to to current date)
export async function endListingDrizzle(listingId: number): Promise<void> {
  try {
    const result = await db
      .update(listings)
      .set({
        effectiveTo: new Date().toISOString().split('T')[0], // Today's date
        updatedAt: new Date().toISOString(),
      })
      .where(eq(listings.id, listingId))
      .returning();

    if (result.length === 0) {
      throw new Error("Listing not found");
    }
  } catch (error) {
    console.error("Error ending listing:", error);
    throw error;
  }
}

// Create a new listing
export async function createListingDrizzle(
  listingData: ListingCreate
): Promise<ListingRead> {
  try {
    const newListing = await db
      .insert(listings)
      .values({
        partyId: listingData.party_id,
        vehicleId: listingData.vehicle_id,
        effectiveFrom: listingData.effective_from,
        effectiveTo: listingData.effective_to,
        fractionOffer: listingData.fraction_offer,
        askingPrice: listingData.asking_price,
        condition: listingData.condition,
        mileage: listingData.mileage,
        listingType: listingData.listing_type,
        audience: listingData.audience,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),

      })
      .returning();

    return {
      id: newListing[0].id,
      partyId: newListing[0].partyId,
      vehicleId: newListing[0].vehicleId,
      effectiveFrom: newListing[0].effectiveFrom,
      effectiveTo: newListing[0].effectiveTo,
      fractionOffer: newListing[0].fractionOffer,
      askingPrice: newListing[0].askingPrice,
      condition: newListing[0].condition as "new" | "used",
      mileage: newListing[0].mileage || undefined,
      listingType: newListing[0].listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: newListing[0].audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: newListing[0].createdAt || undefined,
      updatedAt: newListing[0].updatedAt || undefined,
    };
  } catch (error) {
    console.error("Error creating listing:", error);
    throw error;
  }
}

// Get listing by ID with related data
export async function getListingByIdDrizzle(
  listingId: number
): Promise<ListingRead | null> {
  try {
    // First get the listing with vehicle details
    const listing = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fractionOffer: listings.fractionOffer,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        vehicleCountry: vehicles.countryOfRegistration,
        vehiclePurchaseDate: vehicles.purchaseDate,
        vehicleModelId: vehicles.modelId,
        vehiclePartyId: vehicles.partyId,
        vehicleIsActive: vehicles.isActive,
        vehicleCreatedAt: vehicles.createdAt,
        vehicleUpdatedAt: vehicles.updatedAt,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        modelTransmission: vehicleModel.transmission,
        modelMakeId: vehicleModel.makeId,
        // Make details
        makeName: vehicleMake.name,
        makeId: vehicleMake.id,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(eq(listings.id, listingId))
      .limit(1);

    if (listing.length === 0) return null;

    const record = listing[0];

    // Get listing media separately
    const mediaRecords = await db
      .select({
        id: listingMedia.id,
        listingId: listingMedia.listingId,
        mediaPath: listingMedia.mediaPath,
        createdAt: listingMedia.createdAt,
        updatedAt: listingMedia.updatedAt,
      })
      .from(listingMedia)
      .where(eq(listingMedia.listingId, listingId));
    
    return {
      id: record.id,
      partyId: record.partyId,
      vehicleId: record.vehicleId,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      fractionOffer: record.fractionOffer,
      askingPrice: record.askingPrice,
      condition: record.condition as "new" | "used",
      mileage: record.mileage || undefined,
      listingType: record.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: record.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleVin && record.vehiclePartyId && record.vehicleModelId ? {
        id: record.vehicleId,
        partyId: record.vehiclePartyId,
        modelId: record.vehicleModelId,
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        countryOfRegistration: record.vehicleCountry || undefined,
        manufacturingYear: record.vehicleYear || undefined,
        purchaseDate: record.vehiclePurchaseDate || undefined,
        color: record.vehicleColor || undefined,
        isActive: record.vehicleIsActive || true,
        createdAt: record.vehicleCreatedAt || undefined,
        updatedAt: record.vehicleUpdatedAt || undefined,
        model: record.modelName ? {
          id: record.vehicleModelId,
          model: record.modelName,
          yearModel: record.modelYear || 0,
          transmission: record.modelTransmission || undefined,
          make: record.makeName && record.makeId ? {
            id: record.makeId,
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
      media: mediaRecords.length > 0 ? mediaRecords.map(media => ({
        id: media.id,
        listingId: media.listingId,
        mediaPath: media.mediaPath,
        createdAt: media.createdAt || undefined,
        updatedAt: media.updatedAt || undefined,
      })) : undefined,
    };
  } catch (error) {
    console.error("Error fetching listing:", error);
    throw error;
  }
}

// Get all listings with filters
export async function getAllListingsDrizzle(options: {
  page?: number;
  limit?: number;
  audience?: "BUSINESS" | "E_HAILING" | "CONSUMER";
  listingType?: "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE";
  condition?: "new" | "used";
  maxPrice?: number;
  minPrice?: number;
  sortBy?: "createdAt" | "askingPrice" | "fractionOffer";
  sortOrder?: "asc" | "desc";
} = {}): Promise<{
  records: ListingRead[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}> {
  try {
    const {
      page = 1,
      limit = 20,
      audience,
      listingType,
      condition,
      maxPrice,
      minPrice,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = options;

    const offset = (page - 1) * limit;

    // Build where clause
    let whereConditions = [];
    
    if (audience) {
      whereConditions.push(eq(listings.audience, audience));
    }
    if (listingType) {
      whereConditions.push(eq(listings.listingType, listingType));
    }
    if (condition) {
      whereConditions.push(eq(listings.condition, condition));
    }
    if (maxPrice) {
      whereConditions.push(lte(listings.askingPrice, maxPrice));
    }
    if (minPrice) {
      whereConditions.push(gte(listings.askingPrice, minPrice));
    }
    
    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Build sort order
    const orderByClause = (() => {
      const column = sortBy === "askingPrice" ? listings.askingPrice
                   : sortBy === "fractionOffer" ? listings.fractionOffer
                   : listings.createdAt;
      
      return sortOrder === "desc" ? desc(column) : asc(column);
    })();

    // Get total count
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(listings)
      .where(whereClause);
    
    const total = totalResult[0].count;

    // Get records with related data
    const records = await db
      .select({
        id: listings.id,
        partyId: listings.partyId,
        vehicleId: listings.vehicleId,
        effectiveFrom: listings.effectiveFrom,
        effectiveTo: listings.effectiveTo,
        fractionOffer: listings.fractionOffer,
        askingPrice: listings.askingPrice,
        condition: listings.condition,
        mileage: listings.mileage,
        listingType: listings.listingType,
        audience: listings.audience,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        // Vehicle details
        vehicleVin: vehicles.vinNumber,
        vehicleRegistration: vehicles.vehicleRegistration,
        vehicleColor: vehicles.color,
        vehicleYear: vehicles.manufacturingYear,
        // Model details
        modelName: vehicleModel.model,
        modelYear: vehicleModel.yearModel,
        // Make details
        makeName: vehicleMake.name,
        // Owner details
        ownerFirstName: individual.firstName,
        ownerLastName: individual.lastName,
      })
      .from(listings)
      .leftJoin(vehicles, eq(listings.vehicleId, vehicles.id))
      .leftJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .leftJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .leftJoin(party, eq(listings.partyId, party.id))
      .leftJoin(individual, eq(party.id, individual.partyId))
      .where(whereClause)
      .orderBy(orderByClause)
      .limit(limit)
      .offset(offset);

    // Get listing media for all listings
    const listingIds = records.map(record => record.id);
    const mediaRecords = listingIds.length > 0 ? await db
      .select({
        id: listingMedia.id,
        listingId: listingMedia.listingId,
        mediaPath: listingMedia.mediaPath,
        createdAt: listingMedia.createdAt,
        updatedAt: listingMedia.updatedAt,
      })
      .from(listingMedia)
      .where(inArray(listingMedia.listingId, listingIds)) : [];

    // Group media by listing ID
    const mediaByListingId = mediaRecords.reduce((acc, media) => {
      if (!acc[media.listingId]) {
        acc[media.listingId] = [];
      }
      acc[media.listingId].push({
        id: media.id,
        listingId: media.listingId,
        mediaPath: media.mediaPath,
        createdAt: media.createdAt || undefined,
        updatedAt: media.updatedAt || undefined,
      });
      return acc;
    }, {} as Record<number, ListingMediaRead[]>);

    const formattedRecords: ListingRead[] = records.map(record => ({
      id: record.id,
      partyId: record.partyId,
      vehicleId: record.vehicleId,
      effectiveFrom: record.effectiveFrom,
      effectiveTo: record.effectiveTo,
      fractionOffer: record.fractionOffer,
      askingPrice: record.askingPrice,
      condition: record.condition as "new" | "used",
      mileage: record.mileage || undefined,
      listingType: record.listingType as "SHORT_TERM_LEASE_OUT" | "LONG_TERM_LEASE_OUT" | "CO_OWNERSHIP_SALE",
      audience: record.audience as "BUSINESS" | "E_HAILING" | "CONSUMER",
      createdAt: record.createdAt || undefined,
      updatedAt: record.updatedAt || undefined,
      vehicle: record.vehicleVin ? {
        id: record.vehicleId,
        partyId: record.partyId,
        modelId: 0,
        vinNumber: record.vehicleVin,
        vehicleRegistration: record.vehicleRegistration || undefined,
        color: record.vehicleColor || undefined,
        manufacturingYear: record.vehicleYear || undefined,
        isActive: true,
        model: record.modelName ? {
          id: 0,
          model: record.modelName,
          yearModel: record.modelYear || 0,
          make: record.makeName ? {
            id: 0,
            name: record.makeName,
          } : undefined,
        } : undefined,
      } : undefined,
      owner: record.ownerFirstName && record.ownerLastName ? {
        firstName: record.ownerFirstName,
        lastName: record.ownerLastName,
      } : undefined,
      media: mediaByListingId[record.id] || [],
    }));

    return {
      records: formattedRecords,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  } catch (error) {
    console.error("Error fetching listings:", error);
    throw error;
  }
}

// ==================== COMBINED OPERATIONS ====================

// Create vehicle and listing together from form data
export async function createVehicleAndListingDrizzle(
  formData: ListingFormData,
  partyId: number
): Promise<{ vehicle: VehicleRead; listing: ListingRead }> {
  try {
    // Look up vehicle make by name
    const vehicleMakes = await db
      .select({ id: vehicleMake.id, name: vehicleMake.name })
      .from(vehicleMake)
      .where(eq(vehicleMake.name, formData.make))
      .limit(1);

    let makeId: number;
    if (vehicleMakes.length === 0) {
      // Create new make if it doesn't exist
      const newMake = await db
        .insert(vehicleMake)
        .values({
          name: formData.make,
          description: `${formData.make} vehicles`,
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();
      makeId = newMake[0].id;
    } else {
      makeId = vehicleMakes[0].id;
    }

    // Look up vehicle model by name and make
    const vehicleModels = await db
      .select({ id: vehicleModel.id })
      .from(vehicleModel)
      .where(
        and(
          eq(vehicleModel.makeId, makeId),
          eq(vehicleModel.model, formData.model),
          eq(vehicleModel.yearModel, parseInt(formData.year))
        )
      )
      .limit(1);

    let modelId: number;
    if (vehicleModels.length === 0) {
      // Create new model if it doesn't exist
      const newModel = await db
        .insert(vehicleModel)
        .values({
          makeId: makeId,
          model: formData.model,
          yearModel: parseInt(formData.year),
          description: `${formData.make} ${formData.model} ${formData.year}`,
          transmission: undefined, // Could be added to form later
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        })
        .returning();
      modelId = newModel[0].id;
    } else {
      modelId = vehicleModels[0].id;
    }
    
    // Use fraction offer from form data, fallback to calculated value
    const fractionOffer = formData.fractionOffer 
      ? parseFloat(formData.fractionOffer)
      : formData.fractionSize === "equal" 
        ? 1 / formData.fractions 
        : 0.25; // Default for custom fractions
    
    // Create vehicle first
    const vehicleData: VehicleCreate = {
      partyId,
      modelId,
      vinNumber: formData.vinNumber || `VIN${Date.now()}`, // Generate VIN if not provided
      vehicleRegistration: formData.vehicleRegistration,
      countryOfRegistration: "South Africa",
      manufacturingYear: parseInt(formData.year),
      color: formData.color,
    };
    
    const vehicle = await createVehicleDrizzle(vehicleData);
    
    // Create listing
    const listingData: ListingCreate = {
      party_id: partyId,
      vehicle_id: vehicle.id,
      effective_from: formData.effectiveFrom || new Date().toISOString().split('T')[0],
      effective_to: formData.effectiveTo || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
      fraction_offer: fractionOffer,
      asking_price: parseFloat(formData.pricePerFraction.replace(/[^\d.]/g, '')),
      condition: formData.condition as ConditionEnum,
      mileage: parseFloat(formData.mileage.replace(/[^\d.]/g, '')) || undefined,
      listing_type: formData.listingType as ListingTypeEnum,
      audience: formData.audience as AudienceEnum,

    };
    
    const listing = await createListingDrizzle(listingData);
    
    // Save vehicle images to listingMedia table instead of vehicleMedia
    if (formData.vehicleImages && formData.vehicleImages.length > 0) {
      for (const s3Path of formData.vehicleImages) {
        await db.insert(listingMedia).values({
          listingId: listing.id,
          mediaPath: s3Path, // This is now the S3 path from DocumentUpload
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }
    }
    
    return { vehicle, listing };
  } catch (error) {
    console.error("Error creating vehicle and listing:", error);
    throw error;
  }
}

export async function createListing(
  request: CreateListingRequest
): Promise<{ success: boolean; message: string; data?: any }> {
  try {
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    if (!dbId) {
      return {
        success: false,
        message: "User not authenticated"
      };
    }

    // Verify the user owns the vehicle
    const vehicle = await db
      .select({ 
        id: vehicles.id, 
        partyId: vehicles.partyId 
      })
      .from(vehicles)
      .where(eq(vehicles.id, request.vehicle_id))
      .limit(1);

    if (vehicle.length === 0) {
      return {
        success: false,
        message: "Vehicle not found"
      };
    }

    // Check if user has ownership access to this vehicle
    const ownership = await db
      .select({ id: companyOwnership.id })
      .from(companyOwnership)
      .where(
        and(
          eq(companyOwnership.partyId, +dbId),
          eq(companyOwnership.isActive, true)
        )
      );

    const hasAccess = ownership.some(o => 
      // User has direct ownership or company access
      vehicle[0].partyId === +dbId || 
      // Or vehicle belongs to a company the user is part of
      // We'll need to check company relationships here
      true // Simplified for now
    );

    if (!hasAccess) {
      return {
        success: false,
        message: "You don't have permission to list this vehicle"
      };
    }

    // Create the listing using database schema field names
    const listingData = {
      partyId: +dbId,
      vehicleId: request.vehicle_id,
      listingType: request.listing_type,
      audience: request.audience,
      condition: request.condition,
      askingPrice: request.asking_price,
      fractionOffer: request.fraction_offer,
      effectiveFrom: request.effective_from,
      effectiveTo: request.effective_to,
      mileage: request.mileage,
    };

    const insertedListing = await db.insert(listings).values(listingData).returning();

    return {
      success: true,
      message: "Vehicle listing created successfully",
      data: {
        listing_id: insertedListing[0]?.id,
        vehicle_id: request.vehicle_id
      }
    };

  } catch (error) {
    console.error("Error creating listing:", error);
    return {
      success: false,
      message: "Failed to create listing. Please try again."
    };
  }
}

export async function getUserVehiclesForListing() {
  try {
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    if (!dbId) {
      return [];
    }

    // Get companies where user has ownership
    const userCompanies = await db
      .select({ companyPartyId: company.partyId })
      .from(companyOwnership)
      .innerJoin(company, eq(companyOwnership.companyId, company.id))
      .where(
        and(
          eq(companyOwnership.partyId, +dbId),
          eq(companyOwnership.isActive, true)
        )
      );

    // Build list of party IDs that user has access to (direct ownership + company ownership)
    const accessiblePartyIds = [+dbId, ...userCompanies.map(c => c.companyPartyId)];

    // Get vehicles owned by user directly or through companies user is part of
    const userVehicles = await db
      .select({
        id: vehicles.id,
        vinNumber: vehicles.vinNumber,
        partyId: vehicles.partyId,
        modelId: vehicles.modelId,
        vehicleRegistration: vehicles.vehicleRegistration,
        countryOfRegistration: vehicles.countryOfRegistration,
        manufacturingYear: vehicles.manufacturingYear,
        color: vehicles.color,
        isActive: vehicles.isActive,
        makeId: vehicleMake.id,
        makeName: vehicleMake.name,
        modelName: vehicleModel.model,
        yearModel: vehicleModel.yearModel,
        transmission: vehicleModel.transmission,
      })
      .from(vehicles)
      .innerJoin(vehicleModel, eq(vehicles.modelId, vehicleModel.id))
      .innerJoin(vehicleMake, eq(vehicleModel.makeId, vehicleMake.id))
      .where(
        and(
          eq(vehicles.isActive, true),
          inArray(vehicles.partyId, accessiblePartyIds)
        )
      )
      .orderBy(vehicles.id);

    return userVehicles.map(vehicle => ({
      id: vehicle.id,
      party_id: vehicle.partyId,
      model_id: vehicle.modelId,
      vin_number: vehicle.vinNumber,
      vehicle_registration: vehicle.vehicleRegistration,
      country_of_registration: vehicle.countryOfRegistration,
      manufacturing_year: vehicle.manufacturingYear,
      color: vehicle.color,
      is_active: vehicle.isActive,
      model: {
        id: vehicle.modelId,
        make_id: vehicle.makeId,
        model: vehicle.modelName,
        year_model: vehicle.yearModel,
        transmission: vehicle.transmission,
        make: {
          id: vehicle.makeId,
          name: vehicle.makeName
        }
      }
    }));

  } catch (error) {
    console.error("Error getting user vehicles:", error);
    return [];
  }
}

export async function getUserCompanies() {
  try {
    const userAttributes = await getUserAttributes();
    const { ["custom:db_id"]: dbId } = userAttributes || {};
    if (!dbId) {
      return [];
    }

    // Get companies where user has ownership
    const userCompanies = await db
      .select({
        id: company.id,
        partyId: company.partyId,
        alias: company.alias,
        description: company.description,
        fraction: companyOwnership.fraction,
      })
      .from(companyOwnership)
      .innerJoin(company, eq(companyOwnership.companyId, company.id))
      .where(
        and(
          eq(companyOwnership.partyId, +dbId),
          eq(companyOwnership.isActive, true)
        )
      )
      .orderBy(company.alias);

    return userCompanies.map(comp => ({
      id: comp.id,
      party_id: comp.partyId,
      alias: comp.alias || "Unnamed Group",
      description: comp.description,
      user_fraction: parseFloat(comp.fraction || "0"),
    }));

  } catch (error) {
    console.error("Error getting user companies:", error);
    return [];
  }
} 