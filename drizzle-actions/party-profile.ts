"use server";

/**
 * DRIZZLE ACTIONS - PARTY PROFILE
 * 
 * This file contains all direct database operations for party and party identification management using Drizzle ORM.
 * Replaces API/axios calls with direct database queries.
 */

import { db } from "../db";
import { eq, and, desc, sql } from "drizzle-orm";
import {
  party,
  partyIdentification,
  identificationType,
} from "../drizzle/schema";
import type { PartyRead } from "@/types/party";
import type { PartyIdentificationRead, PartyIdentificationCreate } from "@/types/party-identifications";

// ==================== PARTY OPERATIONS ====================

export async function getPartyByExternalIDDrizzle(
  externalId: string
): Promise<PartyRead | null> {
  const result = await db
    .select({
      id: party.id,
      party_type_id: party.partyTypeId,
      status_id: party.statusId,
      external_id: party.externalId,
      created_at: party.createdAt,
      updated_at: party.updatedAt,
    })
    .from(party)
    .where(eq(party.externalId, externalId))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  const row = result[0];
  return {
    id: row.id,
    party_type_id: row.party_type_id,
    status_id: row.status_id,
    external_id: row.external_id ?? undefined,
    created_at: row.created_at ?? "",
    updated_at: row.updated_at ?? "",
  };
}

export async function getPartyDrizzle(partyId: number): Promise<PartyRead | null> {
  const result = await db
    .select({
      id: party.id,
      party_type_id: party.partyTypeId,
      status_id: party.statusId,
      external_id: party.externalId,
      created_at: party.createdAt,
      updated_at: party.updatedAt,
    })
    .from(party)
    .where(eq(party.id, partyId))
    .limit(1);

  if (result.length === 0) {
    return null;
  }

  const row = result[0];
  return {
    id: row.id,
    party_type_id: row.party_type_id,
    status_id: row.status_id,
    external_id: row.external_id ?? undefined,
    created_at: row.created_at ?? "",
    updated_at: row.updated_at ?? "",
  };
}

// ==================== PARTY IDENTIFICATION OPERATIONS ====================

export async function getPartyIdentificationsByPartyDrizzle(
  partyId: number
): Promise<PartyIdentificationRead[]> {
  const result = await db
    .select({
      id: partyIdentification.id,
      party_id: partyIdentification.partyId,
      identification_type_id: partyIdentification.identificationTypeId,
      document_number: partyIdentification.documentNumber,
      issuing_authority: partyIdentification.issuingAuthority,
      issue_date: partyIdentification.issueDate,
      expiry_date: partyIdentification.expiryDate,
      is_verified: partyIdentification.isVerified,
      verification_date: partyIdentification.verificationDate,
      verification_method: partyIdentification.verificationMethod,
      document_image_url: partyIdentification.documentImageUrl,
      created_at: partyIdentification.createdAt,
      updated_at: partyIdentification.updatedAt,
      // Identification type details
      type_id: identificationType.id,
      type_name: identificationType.name,
      type_description: identificationType.description,
      type_is_active: identificationType.isActive,
      type_created_at: identificationType.createdAt,
      type_updated_at: identificationType.updatedAt,
    })
    .from(partyIdentification)
    .leftJoin(identificationType, eq(partyIdentification.identificationTypeId, identificationType.id))
    .where(eq(partyIdentification.partyId, partyId))
    .orderBy(desc(partyIdentification.createdAt));

  return result.map(row => ({
    id: row.id,
    party_id: row.party_id ?? 0,
    identification_type_id: row.identification_type_id,
    document_number: row.document_number,
    issuing_authority: row.issuing_authority ?? undefined,
    issue_date: row.issue_date ?? undefined,
    expiry_date: row.expiry_date ?? undefined,
    is_verified: row.is_verified,
    verification_date: row.verification_date ?? undefined,
    verification_method: row.verification_method ?? undefined,
    document_image_url: row.document_image_url ?? undefined,
    created_at: row.created_at ?? "",
    updated_at: row.updated_at ?? "",
        identification_type: {
        id: row.type_id || 0,
        name: row.type_name || "unknown",
        description: row.type_description ?? undefined,
        is_active: row.type_is_active ?? true,
        created_at: row.type_created_at ?? "",
        updated_at: row.type_updated_at ?? "",
        },
  }));
}

export async function createPartyIdentificationDrizzle(
  data: PartyIdentificationCreate
): Promise<PartyIdentificationRead> {
  const [result] = await db
    .insert(partyIdentification)
    .values({
      partyId: data.party_id,
      identificationTypeId: data.identification_type_id,
      documentNumber: data.document_number,
      issuingAuthority: data.issuing_authority,
      issueDate: data.issue_date,
      expiryDate: data.expiry_date,
      isVerified: data.is_verified ?? false,
      verificationDate: data.verification_date,
      verificationMethod: data.verification_method,
      documentImageUrl: data.document_image_url,
    })
    .returning();

  // Get the type info
  const [typeInfo] = await db
    .select()
    .from(identificationType)
    .where(eq(identificationType.id, data.identification_type_id))
    .limit(1);

  return {
    id: result.id,
    party_id: result.partyId,
    identification_type_id: result.identificationTypeId,
    document_number: result.documentNumber,
    issuing_authority: result.issuingAuthority ?? undefined,
    issue_date: result.issueDate ?? undefined,
    expiry_date: result.expiryDate ?? undefined,
    is_verified: result.isVerified,
    verification_date: result.verificationDate ?? undefined,
    verification_method: result.verificationMethod ?? undefined,
    document_image_url: result.documentImageUrl ?? undefined,
    created_at: result.createdAt ?? "",
    updated_at: result.updatedAt ?? "",
    identification_type: {
      id: typeInfo?.id || 0,
      name: typeInfo?.name || "unknown",
      description: typeInfo?.description ?? undefined,
      is_active: typeInfo?.isActive ?? true,
      created_at: typeInfo?.createdAt ?? "",
      updated_at: typeInfo?.updatedAt ?? "",
    },
  };
}

export async function createPartyIdentificationsDrizzle(_: any, formData: FormData) {
  const partyId = Number(formData.get("partyId"));
  const documentImageUrl = (formData.get("documentUrl") as string)?.trim();
  const identificationTypeId = Number(formData.get("identificationTypeId"));
  const documentNumber = (formData.get("documentNumber") as string)?.trim();

  const errors = [];

  if (!identificationTypeId) errors.push("Select the identification type");
  if (!documentImageUrl) errors.push("File required");
  if (!partyId) errors.push("File Party Required");

  if (errors.length > 0) {
    return { success: false, errors };
  }

  const partyIdentificationData: PartyIdentificationCreate = {
    party_id: partyId,
    identification_type_id: identificationTypeId,
    document_image_url: documentImageUrl,
    document_number: documentNumber,
    is_verified: false,
  };

  try {
    await createPartyIdentificationDrizzle(partyIdentificationData);
    return { success: true, message: "Document uploaded successfully." };
  } catch (error: any) {
    errors.push(error?.details || "An error occurred");
    console.error("Error creating party identification:", error);
    return { success: false, errors };
  }
}