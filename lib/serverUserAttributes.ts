/**
 * Server-Side User Attributes API
 * 
 * ⚠️  SERVER COMPONENTS ONLY - Use this for:
 * - Server Components (pages with no "use client")
 * - Server Actions
 * - API Routes
 * - Middleware
 * 
 * For client components, import from "@/lib/userAttributes" instead
 * 
 * Usage:
 * ```typescript
 * import { getUserAttributes } from "@/lib/serverUserAttributes";
 * 
 * export default async function MyServerPage() {
 *   const attributes = await getUserAttributes();
 *   // ... use attributes
 * }
 * ```
 * 
 * CACHING BEHAVIOR:
 * - ⏱️  Caches for 1 hour using Next.js unstable_cache
 * - 🖥️  Shared across requests on the server
 * - 🔄 Cache automatically clears on authentication errors
 * - 🏷️  Uses cache tags for potential manual invalidation
 */

// Server-side cached function (for server components, actions, and API routes)
export { getUserAttributes } from "./amplifyServerUtils"; 