import outputs from "@/amplify_outputs.json";
import { createServerRunner } from "@aws-amplify/adapter-nextjs";
import { fetchUserAttributes } from "aws-amplify/auth/server";
import { cookies } from "next/headers";
import { unstable_cache } from "next/cache";
import type { FetchUserAttributesOutput } from "aws-amplify/auth";

export const { runWithAmplifyServerContext, createAuthRouteHandlers } =
  createServerRunner({
    config: outputs,
    runtimeOptions: {
      cookies: {
        domain: ".poollysa.com",
        sameSite: "strict",
        maxAge: 60 * 60 * 24 * 7,
      },
    },
  });

async function _getUserAttributes(cookiesSnapshot: any): Promise<FetchUserAttributesOutput | null> {
  try {
    console.log("Attempting to get user attributes via runWithAmplifyServerContext");
    return await runWithAmplifyServerContext({
      nextServerContext: { cookies: () => cookiesSnapshot },
      operation: async (contextSpec) => {
        try {
          console.log("Executing fetchUserAttributes within Amplify server context");
          const attributes = await fetchUserAttributes(contextSpec);
          console.log("Successfully fetched user attributes:", attributes);
          return attributes;
        } catch (error: any) {
          // Handle unauthenticated users gracefully
          if (error.name === 'UserUnAuthenticatedException' || error.message?.includes('authenticated')) {
            console.log("User is not authenticated - returning null");
            return null;
          }
          console.error("Error fetching user attributes inside operation:", error);
          return null;
        }
      },
    });
  } catch (error: any) {
    // Handle unauthenticated users at the outer level too
    if (error.name === 'UserUnAuthenticatedException' || error.message?.includes('authenticated')) {
      console.log("User is not authenticated (outer catch) - returning null");
      return null;
    }
    console.error("Error in getUserAttributes:", error);
    return null;
  }
}

// Cached version of getUserAttributes that expires after 1 hour
const getCachedUserAttributesInternal = unstable_cache(
  _getUserAttributes,
  ['user-attributes'],
  {
    revalidate: 3600, // 1 hour in seconds
    tags: ['user-attributes']
  }
);

/**
 * Server-side cached version of fetchUserAttributes
 * 
 * ⚠️  SERVER COMPONENTS ONLY - Use this for:
 * - Server Components (pages with no "use client")
 * - Server Actions
 * - API Routes
 * - Middleware
 * 
 * For client components, use getCachedUserAttributes from @/lib/userAttributes instead
 * 
 * Caches results using Next.js unstable_cache for 1 hour
 * 
 * @returns User attributes or null if user is not authenticated
 */
export async function getUserAttributes(): Promise<FetchUserAttributesOutput | null> {
  // Get cookies outside the cached function
  const cookiesSnapshot = await cookies();
  
  // Pass cookies as argument to cached function
  return getCachedUserAttributesInternal(cookiesSnapshot);
}
