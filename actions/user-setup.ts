"use server";

import { getAuthorizedFetch } from "@/lib/fetchClient";
import { getUserAttributes } from "@/lib/serverUserAttributes";
import { revalidatePath } from "next/cache";
import { createPartyIndividualUser } from "@/drizzle-actions/party-individual-user";
import { updateUserAttributes } from "aws-amplify/auth";

interface SetupUserResult {
  success: boolean;
  partyId?: number;
  error?: string;
}

export async function setupUserProfile(attributes: any): Promise<SetupUserResult> {
  try {
    
    if (!attributes) {
      return { success: false, error: "Unable to fetch user attributes" };
    }

    const { sub: externalId, email, given_name: firstName, family_name: lastName, phone_number: phoneNumber } = attributes;
    
    if (!externalId || !email) {
      return { success: false, error: "Missing required user cognito attributes (sub or email)" };
    }

    // Check if user already has dbId
    if (attributes["custom:db_id"]) {
      console.log(`✅ USER ALREADY HAS DB ID: ${attributes["custom:db_id"]}`);
      return { success: true, partyId: parseInt(attributes["custom:db_id"]) };
    }

    // Create the party, individual, user, and contact points using the new function
    const result = await createPartyIndividualUser({
      externalId,
      firstName: firstName || "",
      lastName: lastName || "",
      birthDate: "", // Will be set up later during profile setup
      email: email || "",
      phoneNumber: phoneNumber || "",
    });

    // Check if all required components were created successfully
    if (!result?.party?.id || 
        !result?.individual?.id || 
        !result?.user?.id || 
        !result?.emailContactPoint?.id ||
        (!phoneNumber || result?.phoneContactPoint?.id)) {
      return { success: false, error: "Failed to create complete user profile - missing required components" };
    }

    

    
    
    // Revalidate the home page to trigger a refresh
    revalidatePath("/home");
    
    return { success: true, partyId: result.party.id };
  } catch (error: any) {
    console.error("Error setting up user profile:", error);
    return { success: false, error: error.message || "An unexpected error occurred" };
  }
} 