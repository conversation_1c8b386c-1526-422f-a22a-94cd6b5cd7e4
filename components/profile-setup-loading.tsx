"use client";

import { useEffect, useState } from "react";
import { setupUserProfile } from "@/actions/user-setup";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function ProfileSetupLoading({ attributes }: { attributes: any }) {
  const [status, setStatus] = useState<"setting-up" | "success" | "error">("setting-up");
  const [errorMessage, setErrorMessage] = useState("");
  const router = useRouter();

  useEffect(() => {
    const performSetup = async () => {
      try {
        const result = await setupUserProfile(attributes);
        
        if (result.success) {
          setStatus("success");
          // Wait a moment to show success state, then reload the page completely
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } else {
          setStatus("error");
          setErrorMessage(result.error || "Setup failed");
        }
      } catch (error) {
        setStatus("error");
        setErrorMessage("An unexpected error occurred");
        console.error("Setup error:", error);
      }
    };

    performSetup();
  }, [router]);

  const handleRetry = () => {
    setStatus("setting-up");
    setErrorMessage("");
    // Trigger setup again
    setTimeout(() => {
      window.location.reload();
    }, 500);
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-6 py-8">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Poolly Logo */}
        <div className="flex justify-center">
          <Image
            src="/images/poolly-logo.svg"
            alt="Poolly"
            width={120}
            height={40}
            className="animate-pulse"
          />
        </div>

        {/* Status Content */}
        {status === "setting-up" && (
          <>
            <div className="space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#009639] mx-auto"></div>
              <h2 className="text-2xl font-bold text-[#333333] font-['Poppins']">
                Setting up your profile
              </h2>
              <p className="text-[#797879] font-['Poppins']">
                We're preparing everything for you. This will only take a moment...
              </p>
            </div>
          </>
        )}

        {status === "success" && (
          <>
            <div className="space-y-4">
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <svg 
                  className="h-6 w-6 text-[#009639]" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M5 13l4 4L19 7" 
                  />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-[#333333] font-['Poppins']">
                Profile ready!
              </h2>
              <p className="text-[#797879] font-['Poppins']">
                Your profile has been set up successfully. Redirecting...
              </p>
            </div>
          </>
        )}

        {status === "error" && (
          <>
            <div className="space-y-6">
              <div className="h-12 w-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
                <svg 
                  className="h-6 w-6 text-red-600" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M6 18L18 6M6 6l12 12" 
                  />
                </svg>
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-[#333333] font-['Poppins']">
                  Setup Error
                </h2>
                <p className="text-[#797879] font-['Poppins']">
                  {errorMessage}
                </p>
              </div>
              <button
                onClick={handleRetry}
                className="w-full bg-[#009639] text-white py-3 px-6 rounded-full font-semibold font-['Poppins'] hover:bg-[#007A2F] transition-colors"
              >
                Try Again
              </button>
            </div>
          </>
        )}

        {/* Additional Info */}
        <div className="text-center">
          <p className="text-xs text-[#797879] font-['Poppins']">
            Having trouble? Contact our support team for assistance.
          </p>
        </div>
      </div>
    </div>
  );
} 