"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, CheckCircle, AlertCircle, Car, Target, Calendar, DollarSign, Users, X } from "lucide-react";
import { 
  createListing,
  getUserVehiclesForListing,
  getUserCompanies
} from "@/drizzle-actions/listings";
import {
  ListingTypeEnum,
  AudienceEnum,
  ConditionEnum
} from "@/types/listings";

interface ListVehicleFormProps {
  onClose?: () => void;
  onSuccess?: (data: any) => void;
}

export default function ListVehicleForm({
  onClose,
  onSuccess
}: ListVehicleFormProps) {
  const [formData, setFormData] = useState({
    vehicle_id: "",
    listing_type: "",
    audience: "",
    condition: "",
    asking_price: "",
    fraction_offer: "",
    effective_from: "",
    effective_to: "",
    mileage: "",
    company_id: "", // For private group sharing
  });

  const [vehicles, setVehicles] = useState<any[]>([]);
  const [companies, setCompanies] = useState<any[]>([]);
  
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load vehicles and companies on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        const [vehiclesData, companiesData] = await Promise.all([
          getUserVehiclesForListing(),
          getUserCompanies()
        ]);
        setVehicles(vehiclesData);
        setCompanies(companiesData);
      } catch (error) {
        console.error("Error loading initial data:", error);
        setError("Failed to load form data");
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Set default dates when component loads
  useEffect(() => {
    const today = new Date();
    const nextMonth = new Date(today);
    nextMonth.setMonth(today.getMonth() + 1);
    
    setFormData(prev => ({
      ...prev,
      effective_from: today.toISOString().split('T')[0],
      effective_to: nextMonth.toISOString().split('T')[0]
    }));
  }, []);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const selectedVehicle = vehicles.find(v => v.id.toString() === formData.vehicle_id);
  const vehicleName = selectedVehicle ? 
    `${selectedVehicle.model?.year_model || ''} ${selectedVehicle.model?.make?.name || ''} ${selectedVehicle.model?.model || ''}`.trim() 
    : '';

  // Dynamic fields based on listing type
  const showFractionField = formData.listing_type === "CO_OWNERSHIP_SALE";
  const showPrivateGroupOption = formData.audience === "BUSINESS";
  const isLeaseType = formData.listing_type === "SHORT_TERM_LEASE_OUT" || 
                     formData.listing_type === "LONG_TERM_LEASE_OUT";

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.vehicle_id || !formData.listing_type || !formData.audience || !formData.condition) {
      setError("Please fill in all required fields");
      return;
    }

    if (!formData.asking_price || parseFloat(formData.asking_price) <= 0) {
      setError("Please enter a valid asking price");
      return;
    }

    if (showFractionField && (!formData.fraction_offer || parseFloat(formData.fraction_offer) <= 0 || parseFloat(formData.fraction_offer) > 100)) {
      setError("Please enter a valid fraction percentage (1-100%)");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      const request = {
        vehicle_id: parseInt(formData.vehicle_id),
        listing_type: formData.listing_type as ListingTypeEnum,
        audience: formData.audience as AudienceEnum,
        condition: formData.condition as ConditionEnum,
        asking_price: parseFloat(formData.asking_price),
        fraction_offer: showFractionField ? parseFloat(formData.fraction_offer) / 100 : 1.0, // Convert percentage to decimal
        effective_from: formData.effective_from,
        effective_to: formData.effective_to,
        mileage: formData.mileage ? parseFloat(formData.mileage) : undefined,
        company_id: showPrivateGroupOption && formData.company_id && formData.company_id !== "public" ? parseInt(formData.company_id) : undefined,
      };

      const result = await createListing(request);

      if (result.success) {
        setSuccess(true);
        if (onSuccess) {
          onSuccess(result.data);
        }
      } else {
        setError(result.message);
      }
    } catch (error) {
      console.error("Submit error:", error);
      setError("Failed to create listing");
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-[#009639] mb-4" />
          <p className="text-gray-600">Loading your vehicles...</p>
        </div>
      </div>
    );
  }

  // Show empty state if no vehicles after loading
  if (!loading && vehicles.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <Car className="mx-auto mb-4 h-16 w-16 text-gray-400" />
            <h2 className="text-xl font-semibold text-gray-800 mb-2">No vehicles to list</h2>
            <p className="text-gray-600 mb-6">
              You need to add a vehicle to your account before you can create a listing.
            </p>
            <Button 
              onClick={() => window.location.href = "/list-vehicle"} 
              className="bg-[#009639] hover:bg-[#007A2F] w-full"
            >
              Add Your First Vehicle
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="flex items-center justify-center h-64">
        <Card className="w-full max-w-md mx-auto">
          <CardContent className="p-6 text-center">
            <CheckCircle className="mx-auto mb-4 h-16 w-16 text-[#009639]" />
            <h2 className="text-2xl font-bold text-[#009639] mb-2">Listed Successfully!</h2>
            <p className="text-[#333333] mb-6">
              Your vehicle has been listed and is now available to interested parties.
            </p>
            <Button 
              onClick={onClose || (() => {})} 
              className="bg-[#009639] hover:bg-[#007A2F] w-full"
            >
              Continue
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-h-screen overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-[#333333]">List Your Vehicle</h2>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Vehicle Selection */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
              <Car className="h-5 w-5 text-[#009639]" />
              Select Vehicle
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="vehicle_id" className="text-sm text-[#333333] font-medium">
                Vehicle *
              </Label>
              <Select
                value={formData.vehicle_id}
                onValueChange={(value) => handleSelectChange("vehicle_id", value)}
                required
              >
                <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                  <SelectValue placeholder="Choose a vehicle to list" />
                </SelectTrigger>
                <SelectContent>
                  {vehicles.map((vehicle) => (
                    <SelectItem key={vehicle.id} value={vehicle.id.toString()}>
                      {`${vehicle.model?.year_model || ''} ${vehicle.model?.make?.name || ''} ${vehicle.model?.model || ''} - ${vehicle.vehicle_registration || vehicle.vin_number}`.trim()}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {vehicles.length === 0 && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-2">
                  <div className="flex items-center gap-3">
                    <Car className="h-8 w-8 text-yellow-600" />
                    <div>
                      <h4 className="font-medium text-yellow-800">No vehicles available</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        You need to add a vehicle to your account before you can create a listing.
                      </p>
                      <button
                        type="button"
                        onClick={() => {
                          // Navigate to add vehicle - you may need to implement this
                          window.location.href = "/list-vehicle";
                        }}
                        className="mt-2 bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors"
                      >
                        Add Vehicle
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {selectedVehicle && (
              <div className="bg-gray-50 p-3 rounded-lg">
                <h4 className="font-medium text-[#333333] mb-2">{vehicleName}</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
                  <div>VIN: {selectedVehicle.vin_number}</div>
                  <div>Registration: {selectedVehicle.vehicle_registration || 'N/A'}</div>
                  <div>Year: {selectedVehicle.manufacturing_year || 'N/A'}</div>
                  <div>Color: {selectedVehicle.color || 'N/A'}</div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Listing Type and Target Audience */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
              <Target className="h-5 w-5 text-[#009639]" />
              Listing Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="listing_type" className="text-sm text-[#333333] font-medium">
                  Listing Type *
                </Label>
                <Select
                  value={formData.listing_type}
                  onValueChange={(value) => handleSelectChange("listing_type", value)}
                  required
                >
                  <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                    <SelectValue placeholder="Select listing type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="SHORT_TERM_LEASE_OUT">Short-term Lease</SelectItem>
                    <SelectItem value="LONG_TERM_LEASE_OUT">Long-term Lease</SelectItem>
                    <SelectItem value="CO_OWNERSHIP_SALE">Co-ownership Sale</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="audience" className="text-sm text-[#333333] font-medium">
                  Target Audience *
                </Label>
                <Select
                  value={formData.audience}
                  onValueChange={(value) => handleSelectChange("audience", value)}
                  required
                >
                  <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                    <SelectValue placeholder="Select target audience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="CONSUMER">Individual Consumers</SelectItem>
                    <SelectItem value="BUSINESS">Private Groups/Business</SelectItem>
                    <SelectItem value="E_HAILING">E-hailing Drivers</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Private Group Selection (when Business audience is selected) */}
            {showPrivateGroupOption && (
              <div>
                <Label htmlFor="company_id" className="text-sm text-[#333333] font-medium">
                  Private Group (Optional)
                </Label>
                <Select
                  value={formData.company_id}
                  onValueChange={(value) => handleSelectChange("company_id", value)}
                >
                  <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                    <SelectValue placeholder="Select a private group (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public to all businesses</SelectItem>
                    {companies.map((company) => (
                      <SelectItem key={company.id} value={company.id.toString()}>
                        {company.alias}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  Leave blank to make this listing visible to all businesses
                </p>
              </div>
            )}

            <div>
              <Label htmlFor="condition" className="text-sm text-[#333333] font-medium">
                Vehicle Condition *
              </Label>
              <Select
                value={formData.condition}
                onValueChange={(value) => handleSelectChange("condition", value)}
                required
              >
                <SelectTrigger className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]">
                  <SelectValue placeholder="Select vehicle condition" />
                </SelectTrigger>
                                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="used">Used</SelectItem>
                  </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Pricing and Terms */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
              <DollarSign className="h-5 w-5 text-[#009639]" />
              Pricing & Terms
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="asking_price" className="text-sm text-[#333333] font-medium">
                  {isLeaseType ? "Lease Price (per period) *" : "Asking Price *"}
                </Label>
                <Input
                  id="asking_price"
                  name="asking_price"
                  type="number"
                  step="0.01"
                  value={formData.asking_price}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  required
                />
              </div>

              {/* Show fraction field only for co-ownership sales */}
              {showFractionField && (
                <div>
                  <Label htmlFor="fraction_offer" className="text-sm text-[#333333] font-medium">
                    Ownership Fraction (%) *
                  </Label>
                  <Input
                    id="fraction_offer"
                    name="fraction_offer"
                    type="number"
                    min="1"
                    max="100"
                    step="0.1"
                    value={formData.fraction_offer}
                    onChange={handleInputChange}
                    placeholder="25.0"
                    className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                    required={showFractionField}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Percentage of vehicle ownership you're selling (1-100%)
                  </p>
                </div>
              )}

              <div>
                <Label htmlFor="mileage" className="text-sm text-[#333333] font-medium">
                  Current Mileage (km)
                </Label>
                <Input
                  id="mileage"
                  name="mileage"
                  type="number"
                  value={formData.mileage}
                  onChange={handleInputChange}
                  placeholder="50000"
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Availability Period */}
        <Card className="shadow-sm">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-lg text-[#333333]">
              <Calendar className="h-5 w-5 text-[#009639]" />
              Availability Period
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="effective_from" className="text-sm text-[#333333] font-medium">
                  Available From *
                </Label>
                <Input
                  id="effective_from"
                  name="effective_from"
                  type="date"
                  value={formData.effective_from}
                  onChange={handleInputChange}
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  required
                />
              </div>

              <div>
                <Label htmlFor="effective_to" className="text-sm text-[#333333] font-medium">
                  Available Until *
                </Label>
                <Input
                  id="effective_to"
                  name="effective_to"
                  type="date"
                  value={formData.effective_to}
                  onChange={handleInputChange}
                  className="mt-1 border-gray-200 focus:border-[#009639] focus:ring-[#009639]"
                  required
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Alert className="border-red-500 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-700">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          {onClose && (
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose}
              className="order-2 sm:order-1 border-gray-200 text-[#333333] hover:bg-gray-50"
            >
              Cancel
            </Button>
          )}
          
          <Button
            type="submit"
            disabled={
              vehicles.length === 0 ||
              !formData.vehicle_id ||
              !formData.listing_type ||
              !formData.audience ||
              !formData.condition ||
              !formData.asking_price ||
              (showFractionField && !formData.fraction_offer) ||
              submitting
            }
            className="order-1 sm:order-2 bg-[#009639] hover:bg-[#007A2F] flex-1 sm:flex-none sm:min-w-[200px]"
          >
            {submitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating Listing...
              </>
            ) : (
              "Create Listing"
            )}
          </Button>
        </div>
      </form>
    </div>
  );
} 